/// 物资结算-单据表
model MaterialSettlement {
  // 主键 & 外键
  tenantId                          String         @map("tenant_id") /// 租户id
  orgId                             String         @map("org_id") /// 组织id
  id                                String         @id @default(uuid(7)) @map("id") /// 数据id
  // 业务字段
  code                              String         @map("code") /// 单据编码
  settlementType                    SettlementType @map("settlement_type") /// 结算类型
  supplierId                        String?        @map("supplier_id") /// 供应商ID
  supplierName                      String?        @map("supplier_name") /// 供应商名称
  contractId                        String?        @map("contract_id") /// 合同ID
  contractName                      String?        @map("contract_name") /// 合同名称
  contractAmount                    Decimal?       @map("contract_amount") @db.Decimal(20, 6) /// 合同签订金额
  priceType                         String?        @map("price_type") /// 价格类型
  taxRate                           Decimal?       @map("tax_rate") @db.Decimal(20, 6) /// 税率
  taxExcludedAmount                 Decimal?       @map("tax_excluded_amount") @db.Decimal(20, 6) /// 不含税金额
  taxIncludedAmount                 Decimal?       @map("tax_included_amount") @db.Decimal(20, 6) /// 含税金额
  taxAmount                         Decimal?       @map("tax_amount") @db.Decimal(20, 6) /// 税金
  beforeCumulationTaxExcludedAmount Decimal?       @map("before_cumulation_tax_excluded_amount") @db.Decimal(20, 6) /// 期前开累不含税金额
  beforeCumulationTaxIncludedAmount Decimal?       @map("before_cumulation_tax_included_amount") @db.Decimal(20, 6) /// 期前开累含税金额
  beforeCumulationTaxAmount         Decimal?       @map("before_cumulation_tax_amount") @db.Decimal(20, 6) /// 期前开累税金
  settlementDate                    DateTime       @default(now()) @map("settlement_date") /// 结算日期
  submitStatus                      SubmitStatus   @default(PENDING) @map("submit_status") /// 提交状态
  auditStatus                       AuditStatus    @default(PENDING) @map("audit_status") /// 审批状态
  year                              Int            @map("year") /// 年
  month                             Int            @map("month") /// 月
  transferInProjectId               String?        @map("transfer_in_project_id") /// 调入项目id

  materialSettlementDetail         MaterialSettlementDetail[]
  materialSettlementIncomingDetail MaterialSettlementBillRefDetail[]
  materialSettlementAttachment     MaterialSettlementAttachment[]

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_settlement")
}

// 物资结算-明细表
model MaterialSettlementDetail {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  settlementId                String   @map("settlement_id") /// 结算单ID
  materialId                  String?  @map("material_id") /// 材料ID
  materialName                String?  @map("material_name") /// 材料名称
  materialSpec                String?  @map("material_spec") /// 材料规格
  unit                        String?  @map("unit") /// 计量单位
  quantity                    Decimal? @map("quantity") @db.Decimal(20, 8) /// 汇总(收料/退货/调拨)数量
  price                       Decimal? @map("price") @db.Decimal(20, 6) /// 不含税(收料/退货/调拨)单价
  amount                      Decimal? @map("amount") @db.Decimal(20, 6) /// 不含税(收料/退货/调拨)金额
  settlementQuantity          Decimal? @map("settlement_quantity") @db.Decimal(20, 8) /// 汇总(收料/退货/调拨)数量
  settlementPrice             Decimal? @map("settlement_price") @db.Decimal(20, 6) /// 不含税(收料/退货/调拨)单价
  settlementAmount            Decimal? @map("settlement_amount") @db.Decimal(20, 6) /// 不含税(收料/退货/调拨)金额
  orderNo                     Int      @default(autoincrement()) @map("order_no") /// 排序号
  remark                      String?  @map("remark") /// 备注
  isAutoCalculation           Boolean  @default(true) @map("is_auto_calculation") /// 是否根据单价及数量自动计算金额
  isManual                    Boolean  @default(false) @map("is_manual") /// 是否手动新增
  businessCostSubjectDetailId String?  @map("business_cost_subject_detail_id") /// 业务成本科目明细 id

  materialSettlement MaterialSettlement @relation(fields: [settlementId], references: [id])

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_settlement_detail")
}

// 物资结算-收料单\退货单关联表
model MaterialSettlementBillRefDetail {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  settlementBillType SettlementBillType @map("settlement_bill_type") /// 结算单据类型
  billId             String             @map("bill_id") /// 单据ID
  billDetailId       String             @map("bill_detail_id") /// 单据明细ID
  settlementDetailId String             @map("settlement_detail_id") /// 结算单明细ID
  settlementId       String             @default("") @map("settlement_id") /// 结算单ID

  materialSettlement MaterialSettlement @relation(fields: [settlementId], references: [id])

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_settlement_bill_ref_detail")
}

// 收料单-附件表
model MaterialSettlementAttachment {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  settlementId    String @map("receiving_id") /// 收料单ID
  fileName        String @map("file_name") /// 文件名称
  fileKey         String @map("file_key") /// 文件key
  fileSize        Int    @map("file_size") /// 文件大小
  fileExt         String @map("file_ext") /// 文件后缀
  fileContentType String @map("file_content_type") /// 文件contentType

  materialSettlement MaterialSettlement @relation(fields: [settlementId], references: [id])

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_settlement_attachment")
}
