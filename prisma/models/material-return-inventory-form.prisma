// 退库单-单据表
model MaterialReturnInventoryForm {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  settleStatus        MaterialSettlementStatus @default(UN_SETTLED) @map("settle_status") // 结转状态
  code                String                   @map("code") // 单据编码
  supplierId          String?                  @map("supplier_id") // 退料单位ID
  supplierName        String?                  @map("supplier_name") // 退料单位名称
  amount              Decimal?                 @map("amount") @db.Decimal(20, 6) // 退库金额
  returnInventoryType ReturnInventoryType      @map("return_inventory_type") // 退库类型
  submitStatus        SubmitStatus             @default(PENDING) @map("submit_status") // 提交状态
  auditStatus         AuditStatus              @default(PENDING) @map("audit_status") // 审批状态
  year                Int                      @map("year") // 年
  month               Int                      @map("month") // 月
  day                 Int                      @map("day") // 日

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") // 是否删除
  createBy  String   @default("system") @map("create_by") // 创建人
  updateBy  String   @default("system") @map("update_by") // 更新人
  createAt  DateTime @default(now()) @map("create_at") // 创建时间
  updateAt  DateTime @updatedAt @map("update_at") // 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_return_inventory_form")
}

// 退库单-材料明细表
model MaterialReturnInventoryFormDetail {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  parentId                    String?  @map("parent_id") // 父ID
  materialReceivingId         String?  @map("material_receiving_id") /// 收料单ID
  materialRequisitionDetailId String?  @map("material_requisition_detail_id") /// 领料单明细ID
  materialReversalId          String   @map("material_reversal_id") /// 退库单ID
  materialId                  String   @map("material_id") /// 材料ID
  materialName                String?  @map("material_name") /// 材料名称
  materialSpec                String?  @map("material_spec") /// 材料规格
  unit                        String   @map("unit") /// 计量单位
  partName                    String?  @map("part_name") /// 原领料使用部位名称（子级一致则填写，不一致则不填写）
  businessCostSubjectDetailId String?  @map("business_cost_subject_detail_id") /// 成本科目明细ID(领料的)
  requisitionPrice            Decimal? @map("requisition_price") @db.Decimal(20, 6) // 领料单价
  canReversalQuantity         Decimal  @map("can_reversal_quantity") @db.Decimal(20, 8) // 可退数量
  reversalQuantity            Decimal? @map("reversal_quantity") @db.Decimal(20, 8) // 退库数量
  reversalPrice               Decimal? @map("reversal_price") @db.Decimal(20, 6) // 退库单价
  reversalAmount              Decimal? @map("reversal_amount") @db.Decimal(20, 6) // 退库金额
  depreciationAmount          Decimal? @map("depreciation_amount") @db.Decimal(20, 6) // 折旧摊销金额
  reversalInventoryQuantity   Decimal? @map("reversal_inventory_quantity") @db.Decimal(20, 8) // 当前退库单的库存数量
  orderNo                     Int      @default(autoincrement()) @map("order_no") /// 排序号
  remark                      String?  @map("remark") /// 备注

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_return_inventory_form_detail")
}

// 退库单-附件表
model MaterialReturnInventoryFormAttachment {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  materialReversalId String @map("material_reversal_id") /// 退库单ID
  fileName           String @map("file_name") /// 文件名称
  fileKey            String @map("file_key") /// 文件key
  fileSize           Int    @map("file_size") /// 文件大小
  fileExt            String @map("file_ext") /// 文件后缀
  fileContentType    String @map("file_content_type") /// 文件contentType

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_return_inventory_form_attachment")
}
