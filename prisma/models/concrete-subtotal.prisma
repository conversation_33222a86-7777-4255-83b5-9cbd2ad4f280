// 商品混凝土小计-单据表
model ConcreteSubtotalOrder {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  code         String  @map("code") /// 单据编码
  supplierId   String? @map("supplier_id") /// 供应商ID
  supplierName String? @map("supplier_name") /// 供应商名称
  contractId   String? @map("contract_id") /// 合同ID
  contractName String? @map("contract_name") /// 合同名称
  pouringArea  String? @map("pouring_area") // 浇筑部位

  year  Int @map("year") /// 年
  month Int @map("month") /// 月
  day   Int @map("day") /// 日

  materialSettlementStatus MaterialSettlementStatus @default(UN_SETTLED) @map("material_settlement_status") /// 结算状态
  submitStatus             SubmitStatus             @default(PENDING) @map("submit_status") /// 提交状态
  auditStatus              AuditStatus              @default(PENDING) @map("audit_status") /// 审批状态

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 关联关系
  ConcreteSubtotalDetail     ConcreteSubtotalDetail[]
  ConcreteSubtotalAttachment ConcreteSubtotalAttachment[]

  @@unique([tenantId, orgId, id])
  @@map("concrete_subtotal_order")
}

// 商品混凝土小计-单据明细表
model ConcreteSubtotalDetail {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  concreteSubtotalOrderId String @map("concrete_subtotal_orderId") /// 商品混凝土单据id

  materialName          String  @map("material_name") /// 材料名称
  materialSpec          String  @map("material_spec") /// 材料规格
  unit                  String? @map("unit") /// 计量单位
  pouringType           String? @map("pouring_type") // 浇筑方式
  receiptPages          Int?    @map("receipt_pages") // 小票张数
  receiptCount          Int?    @map("receipt_count") // 小票数量
  deductionRate         Int?    @map("deduction_rate") // 扣除比例
  surchargeId           String? @map("surcharge_id") // 附加费
  businessCostSubjectId String? @map("business_cost_subject_id") // 成本科目ID

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  // 关联关系
  concreteSubtotalOrder           ConcreteSubtotalOrder             @relation(fields: [concreteSubtotalOrderId], references: [id])
  ConcreteSubtotalDetailSurcharge ConcreteSubtotalDetailSurcharge[]

  @@unique([tenantId, orgId, id])
  @@map("concrete_subtotal_detail")
}

// 商品混凝土小计-单据明细/附加费 中间表
model ConcreteSubtotalDetailSurcharge {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  concreteSubtotalDetailId    String @map("concrete_subtotal_detail_id") /// 商品混凝土明细id
  contractConcreteSurchargeId String @map("contract_concrete_surcharge_id") /// 商品混凝土附加费id

  // 关联关系
  concreteSubtotalDetail    ConcreteSubtotalDetail    @relation(fields: [concreteSubtotalDetailId], references: [id])
  contractConcreteSurcharge ContractConcreteSurcharge @relation(fields: [contractConcreteSurchargeId], references: [id])

  @@unique([tenantId, orgId, id])
  @@map("concrete_subtotal_detail_surcharge")
}

// 商品混凝土小计-附件表
model ConcreteSubtotalAttachment {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  concreteSubtotalOrderId String @map("concrete_subtotal_orderId") /// 商品混凝土单据id
  fileName                String @map("file_name") /// 文件名称
  fileKey                 String @map("file_key") /// 文件key
  fileSize                Int    @map("file_size") /// 文件大小
  fileExt                 String @map("file_ext") /// 文件后缀
  fileContentType         String @map("file_content_type") /// 文件contentType

  // 关联关系
  concreteSubtotalOrder ConcreteSubtotalOrder @relation(fields: [concreteSubtotalOrderId], references: [id])

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("concrete_subtotal_attachment")
}
