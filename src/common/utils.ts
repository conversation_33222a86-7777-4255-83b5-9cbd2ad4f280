export class Utils {
  /**
   * 构建树结构
   * @param list
   * @param parentId
   * @returns
   */

  static buildTree(list: any[], parentId: number | null) {
    const result: any[] = [];

    for (const item of list) {
      if (item.parentId === parentId) {
        const children = this.buildTree(list, item.id);

        if (children.length) {
          item.children = children;
        }

        result.push(item);
      }
    }

    return result;
  }

  /**
   * 自动转换文件大小单位（字节 → KB/MB/GB/TB）
   * @param bytes 文件大小（字节）
   * @param options 格式化选项
   * @returns 格式化后的文件大小字符串（如 "2.45 MB"）
   */
  static formatFileSize(
    bytes: number,
    options?: {
      decimalPlaces?: number; // 小数位数，默认 2
      spaceBeforeUnit?: boolean; // 单位前是否添加空格，默认 true
      unit?: 'auto' | 'B' | 'KB' | 'MB' | 'GB' | 'TB'; // 强制使用某个单位，默认 'auto'
    }
  ): string {
    // 处理非正数或无效值
    if (bytes <= 0) return '0 B';

    // 配置默认值
    const {
      decimalPlaces = 2,
      spaceBeforeUnit = true,
      unit = 'auto'
    } = options || {};

    // 单位列表及对应的字节数
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    const space = spaceBeforeUnit ? ' ' : '';

    // 如果指定了单位，则直接转换
    if (unit !== 'auto') {
      const unitIndex = units.indexOf(unit);
      if (unitIndex === -1) throw new Error(`Invalid unit: ${unit}`);

      const value = bytes / Math.pow(1024, unitIndex);
      return `${value.toFixed(decimalPlaces)}${space}${unit}`;
    }

    // 自动选择合适的单位
    let unitIndex = 0;
    let size = bytes;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(decimalPlaces)}${space}${units[unitIndex]}`;
  }

  /**
   * 将数字转换为财务中文大写（支持小数）
   * @param num 要转换的数字或数字字符串
   * @returns 中文大写字符串，如：壹拾贰亿叁仟肆佰伍拾陆万柒仟捌佰玖拾元整
   */
  static numberToFinancialChinese(num: number | string | null): string | null {
    if (num === null) {
      return null;
    }
    // 基本数字映射
    const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    // 单位级别（万、亿、兆等，可以按需扩展）
    const unitLevels = ['', '万', '亿', '兆'];
    // 节内数字单位（十、百、千）
    const sectionUnits = ['', '拾', '佰', '仟'];
    // 小数部分单位
    const decimalUnits = ['角', '分', '厘', '毫'];

    // 处理输入
    let numStr = String(num).trim();
    if (!/^-?\d+(\.\d+)?$/.test(numStr)) {
      throw new Error('Invalid number format');
    }

    // 处理负数
    const isNegative = numStr.startsWith('-');
    if (isNegative) {
      numStr = numStr.slice(1);
    }

    // 分割整数和小数部分
    const [integerPart, decimalPart = ''] = numStr.split('.');

    // 转换整数部分
    let result = '';
    if (integerPart === '0') {
      result = digits[0];
    } else {
      // 每4位一节进行处理
      const sections = [];
      let remaining = integerPart;
      while (remaining.length > 0) {
        sections.unshift(remaining.slice(-4));
        remaining = remaining.slice(0, -4);
      }

      // 处理每一节
      for (let i = 0; i < sections.length; i++) {
        const section = sections[i];
        if (section === '0000') continue;

        let sectionResult = '';
        for (let j = 0; j < section.length; j++) {
          const digit = parseInt(section[j], 10);
          const position = section.length - j - 1;

          if (digit === 0) {
            // 处理零
            if (j > 0 && parseInt(section[j - 1], 10) !== 0) {
              sectionResult += digits[digit];
            }
          } else {
            sectionResult += digits[digit] + sectionUnits[position];
          }
        }

        // 添加节级别单位（万、亿等）
        sectionResult += unitLevels[sections.length - i - 1];
        result += sectionResult;
      }
    }

    // 添加"元"字
    result += '元';

    // 处理小数部分
    if (decimalPart) {
      // 最多处理4位小数（厘、毫）
      const decimalDigits = decimalPart.slice(0, 4).padEnd(4, '0');
      let hasDecimal = false;

      for (let i = 0; i < decimalDigits.length; i++) {
        const digit = parseInt(decimalDigits[i], 10);
        if (digit !== 0) {
          result += digits[digit] + decimalUnits[i];
          hasDecimal = true;
        }
      }

      // 如果没有小数部分，添加"整"字
      if (!hasDecimal) {
        result += '整';
      }
    } else {
      // 没有小数部分，添加"整"字
      result += '整';
    }

    // 添加负数前缀
    if (isNegative) {
      result = '负' + result;
    }

    return result;
  }

  //  深查判断引用
  // deepSearch(PrismaClient, parentId) {

  // }

  static calculateEmptyRowCount(pageCount: number, totalCount: number): number {
    const lastPageRowCount = totalCount % pageCount;
    return lastPageRowCount === 0 ? 0 : pageCount - lastPageRowCount;
  }
}
