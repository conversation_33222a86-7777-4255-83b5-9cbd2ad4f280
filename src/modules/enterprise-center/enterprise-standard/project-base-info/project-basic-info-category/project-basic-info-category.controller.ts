import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Patch,
  Post
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import { ProjectBasicInfoFieldDetailService } from '../project-basic-info-field-detail/project-basic-info-field-detail.service';
import {
  CreateProjectBasicInfoCategoryDto,
  MoveProjectBasicDto,
  UpdateProjectBasicInfoCategoryDto,
  UpdateProjectBasicPublishTypeDto
} from './project-basic-info-category.dto';
import { ProjectBasicInfoCategoryService } from './project-basic-info-category.service';

@ApiTags('项目基础信息-项目分组')
@Controller('project-basic-info-category')
export class ProjectBasicInfoCategoryController {
  constructor(
    private readonly service: ProjectBasicInfoCategoryService,
    private readonly projectBasicInfoFieldDetailService: ProjectBasicInfoFieldDetailService
  ) {}

  @ApiOperation({
    summary: '新增分组',
    description: '新增分组'
  })
  @ApiResponse({ status: 200, description: '新增分组成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/add')
  async create(
    @ReqUser() reqUser: IReqUser,
    @Body() data: CreateProjectBasicInfoCategoryDto
  ) {
    // 校验名称
    const isExist = await this.service.getObjByName(reqUser, data.name);
    if (isExist) {
      throw new HttpException('名称已存在', HttpStatus.BAD_REQUEST);
    }
    return await this.service.add(data, reqUser);
  }

  @ApiOperation({
    summary: '编辑分组',
    description: '编辑分组'
  })
  @ApiResponse({ status: 200, description: '编辑分组成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Patch('/edit/:id')
  async edit(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() data: UpdateProjectBasicInfoCategoryDto
  ) {
    const category = await this.service.getObj(id, reqUser);
    if (category?.isPublish) {
      throw new HttpException('已发布分组不可编辑', HttpStatus.BAD_REQUEST);
    }
    if (category?.isDefault) {
      throw new HttpException('默认分组不可编辑', HttpStatus.BAD_REQUEST);
    }
    // 校验名称不包含自己
    const isExist = await this.service.getObjByNameNotInOwer(
      id,
      reqUser,
      data.name
    );
    if (isExist) {
      throw new HttpException('名称已存在', HttpStatus.BAD_REQUEST);
    }
    return await this.service.edit(id, data, reqUser);
  }

  @ApiOperation({
    summary: '查询分组',
    description: '查询分组'
  })
  @ApiResponse({ status: 200, description: '查询分组成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/list')
  async list(@ReqUser() reqUser: IReqUser) {
    return await this.service.getList(reqUser);
  }

  @ApiOperation({
    summary: '删除分组',
    description: '删除分组'
  })
  @ApiResponse({ status: 200, description: '删除分组成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Delete('/del/:id')
  async del(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    const data = await this.service.getObj(id, reqUser);
    if (data?.isPublish) {
      throw new HttpException('已发布分组不能删除', HttpStatus.BAD_REQUEST);
    }
    if (data?.isDefault) {
      throw new HttpException('默认分组不能删除', HttpStatus.BAD_REQUEST);
    }
    // 根据分组查询字段
    const field = await this.projectBasicInfoFieldDetailService.getUseList(
      id,
      reqUser
    );
    if (field.length) {
      throw new HttpException('分组下有数据，不可删除', HttpStatus.BAD_REQUEST);
    }
    return await this.service.del(id, reqUser);
  }

  @ApiOperation({
    summary: '查询分组对象',
    description: '查询分组对象'
  })
  @ApiResponse({ status: 200, description: '查询分组对象成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/obj/:id')
  async obj(@ReqUser() reqUser: IReqUser, @Param('id') id: string) {
    return await this.service.getObj(id, reqUser);
  }

  @ApiOperation({
    summary: '修改上移下移',
    description: '修改上移下移'
  })
  @ApiResponse({ status: 200, description: '修改上移下移成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Post('/edit/move')
  async editMove(
    @ReqUser() reqUser: IReqUser,
    @Body() data: MoveProjectBasicDto
  ) {
    const { id, moveType } = data;
    const category = await this.service.getObj(id, reqUser);
    if (category?.isPublish) {
      throw new HttpException('已发布分组不可移动', HttpStatus.BAD_REQUEST);
    }
    if (category?.isDefault) {
      throw new HttpException('默认分组不可移动', HttpStatus.BAD_REQUEST);
    }
    if (moveType === 'up') {
      // 上移
      await this.service.up(id, reqUser);
    } else {
      // 下移
      await this.service.down(id, reqUser);
    }
    return true;
  }

  @ApiOperation({
    summary: '分组发布',
    description: '分组发布'
  })
  @ApiResponse({ status: 200, description: '分组发布成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Patch('/publish')
  async publish(
    @ReqUser() reqUser: IReqUser,
    @Body() data: UpdateProjectBasicPublishTypeDto
  ) {
    return await this.service.publish(reqUser, data);
  }

  @ApiOperation({
    summary: '查询分组状态',
    description: '查询分组状态'
  })
  @ApiResponse({ status: 200, description: '查询分组状态成功' })
  @ApiResponse({ status: 500, description: '参数错误' })
  @Get('/status')
  async getStatus(@ReqUser() reqUser: IReqUser) {
    return await this.service.getStatus(reqUser);
  }
}
