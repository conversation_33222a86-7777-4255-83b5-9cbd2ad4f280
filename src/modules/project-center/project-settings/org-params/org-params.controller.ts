import { Controller, Get, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  HasBaseAccountResDto,
  OrgParamsBaseAccountDto
} from './org-params.dto';
import { OrgParamsService } from './org-params.service';

@ApiTags('项目参数设置')
@Controller('org-params')
export class OrgParamsController {
  constructor(private readonly service: OrgParamsService) {}

  @ApiOperation({ summary: '当前项目下是否有基础单据' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: HasBaseAccountResDto
  })
  @Get('/has-base-account')
  async hasBaseAccount(
    @ReqUser() reqUser: IReqUser,
    @Query() query: OrgParamsBaseAccountDto
  ) {
    return await this.service.hasBaseAccount(reqUser, query);
  }
}
