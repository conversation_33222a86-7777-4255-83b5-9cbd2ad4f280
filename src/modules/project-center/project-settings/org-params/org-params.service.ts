import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import {
  HasBaseAccountResDto,
  OrgParamsBaseAccountDto
} from './org-params.dto';

@Injectable()
export class OrgParamsService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 当前项目下是否有基础单据
   * @param reqUser
   * @returns
   */
  async hasBaseAccount(
    reqUser: IReqUser,
    query: OrgParamsBaseAccountDto
  ): Promise<HasBaseAccountResDto> {
    const { tenantId } = reqUser;
    const orgId = query.orgId;
    // TODO: 基础单据包括结算单、收发耗用结存
    const firstItem = await this.prisma.materialSettlement.findFirst({
      where: {
        orgId,
        tenantId,
        isDeleted: false
      }
    });
    return {
      hasBaseAccount: !!firstItem
    };
  }
}
