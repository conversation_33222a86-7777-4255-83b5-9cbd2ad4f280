import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';
import { PlatformModule } from '@/modules/platform/platform.module';

import { MaterialSettlementModule } from '../material-settlement/material-settlement.module';
import { MaterialReceivingController } from './material-receiving/material-receiving.controller';
import { MaterialReceivingService } from './material-receiving/material-receiving.service';
import { MaterialReceivingAttachmentController } from './material-receiving-attachment/material-receiving-attachment.controller';
import { MaterialReceivingAttachmentService } from './material-receiving-attachment/material-receiving-attachment.service';
import { MaterialReceivingDetailController } from './material-receiving-detail/material-receiving-detail.controller';
import { MaterialReceivingDetailService } from './material-receiving-detail/material-receiving-detail.service';

@Module({
  imports: [PrismaModule, PlatformModule, MaterialSettlementModule],
  controllers: [
    MaterialReceivingController,
    MaterialReceivingAttachmentController,
    MaterialReceivingDetailController
  ],
  providers: [
    MaterialReceivingService,
    MaterialReceivingAttachmentService,
    MaterialReceivingDetailService
  ]
})
export class MaterialReceivingModule {}
