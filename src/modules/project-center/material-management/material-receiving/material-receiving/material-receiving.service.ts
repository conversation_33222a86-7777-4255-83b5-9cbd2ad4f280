import { BadRequestException, Injectable } from '@nestjs/common';
import { Decimal } from '@prisma/client/runtime/library';
import * as dayjs from 'dayjs';

import { TimeListResponseDto } from '@/common/dtos/common.dto';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { PlatformService } from '@/modules/platform/platform.service';
import {
  AuditStatus,
  Prisma,
  PurchaseType,
  SettlementBillType,
  SubmitStatus
} from '@/prisma/generated';

import { MaterialReceivingDetailService } from '../material-receiving-detail/material-receiving-detail.service';
import {
  MaterialReceivingUpdateDto,
  MaterialReceivingUpdateSubmitStatusDto,
  QueryIncomingContractDto,
  QueryIncomingSupplierDto,
  QueryMaterialReceivingDto
} from './material-receiving.dto';

@Injectable()
export class MaterialReceivingService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly platformService: PlatformService,
    private readonly materialReceivingDetailService: MaterialReceivingDetailService
  ) {}

  /**
   * 查询进场验收单下的合同
   * @param reqUser
   * @param query
   * @returns
   */
  async getContractList(reqUser: IReqUser, query: QueryIncomingContractDto) {
    const { tenantId, orgId } = reqUser;
    const { supplierId } = query;
    let contract = await this.prisma.materialIncomingInspection.findMany({
      where: {
        orgId,
        tenantId,
        isDeleted: false,
        submitStatus: 'SUBMITTED',
        auditStatus: 'APPROVED',
        supplierId: supplierId ? supplierId : undefined,
        contractId: {
          gt: ''
        }
      },
      distinct: ['contractId'],
      select: {
        purchaseType: true,
        supplierId: true,
        contractId: true
      }
    });
    const contractIds: string[] = contract.map((item) => item.contractId!);
    const supplierIds: string[] = contract.map((item) => item.supplierId!);
    // 获取合同名称
    contract = await this.getContractName(contractIds, reqUser, contract);
    contract = await this.getSupplierNameForSelect(
      supplierIds,
      reqUser,
      contract
    );
    return contract;
  }

  async getSupplierName(
    supplierIds: string[],
    reqUser: IReqUser,
    contract: any[]
  ) {
    supplierIds = supplierIds.filter((item) => item !== null && item !== '');
    const { tenantId } = reqUser;
    // 获取供应商信息
    const supplierList = await this.prisma.supplierDirectory.findMany({
      where: {
        id: {
          in: supplierIds
        },
        tenantId,
        isDeleted: false
      },
      select: {
        id: true,
        simpleName: true
      }
    });
    // 获取公司基本信息
    const companyInfoList = await this.prisma.businessBaseInfo.findMany({
      where: {
        id: {
          in: supplierIds
        },
        tenantId,
        isDeleted: false
      },
      select: {
        id: true,
        companyVersion: true
      }
    });
    const partAInfoList: Record<string, any>[] = await this.prisma.$queryRaw`
      select
        base_field.id
        ,base_ledger.value
      from basic_project_info_field_detail as base_field
      join basic_project_info_ledger as base_ledger
        on base_ledger.is_deleted = false
        and base_ledger.org_id = ${reqUser.orgId}
        and base_ledger.basic_project_info_field_detail_id = base_field.id
      where base_field.is_deleted = false
        ${supplierIds.length ? Prisma.sql`and base_field.id in (${Prisma.join(supplierIds)})` : Prisma.empty}
    `;
    return contract.map((item) => {
      const supplier = supplierList.find((sup) => sup.id === item.supplierId);
      const partAInfo = partAInfoList.find(
        (partA) => partA.id === item.supplierId
      );
      const companyInfo = companyInfoList.find(
        (company) => company.id === item.supplierId
      );
      if (supplier) {
        return {
          ...item,
          supplierName: supplier?.simpleName
        };
      }
      if (partAInfo) {
        return {
          ...item,
          supplierName: partAInfo?.value
        };
      }
      if (companyInfo) {
        return {
          ...item,
          supplierName: companyInfo?.companyVersion
        };
      }
      if (!supplier && !companyInfo && !partAInfo) {
        return {
          ...item
        };
      }
    });
  }

  async getSupplierNameForSelect(
    supplierIds: string[],
    reqUser: IReqUser,
    contract: any[]
  ) {
    supplierIds = supplierIds.filter((item) => item !== null && item !== '');
    const { tenantId } = reqUser;
    // 获取供应商信息
    const supplierList = await this.prisma.materialContract.findMany({
      where: {
        partyB: {
          in: supplierIds
        },
        tenantId,
        isDeleted: false
      },
      select: {
        id: true,
        partyBEndName: true
      }
    });
    const partAInfoList: Record<string, any>[] = await this.prisma.$queryRaw`
      select
        base_field.id
        ,base_ledger.value
      from basic_project_info_field_detail as base_field
      join basic_project_info_ledger as base_ledger
        on base_ledger.is_deleted = false
        and base_ledger.org_id = ${reqUser.orgId}
        and base_ledger.basic_project_info_field_detail_id = base_field.id
      where base_field.is_deleted = false
        ${supplierIds.length ? Prisma.sql`and base_field.id in (${Prisma.join(supplierIds)})` : Prisma.empty}
    `;
    return contract.map((item) => {
      const supplier = supplierList.find((sup) => sup.id === item.supplierId);
      const partAInfo = partAInfoList.find(
        (partA) => partA.id === item.supplierId
      );
      if (supplier) {
        return {
          ...item,
          supplierName: supplier?.partyBEndName
        };
      }
      if (partAInfo) {
        return {
          ...item,
          supplierName: partAInfo?.value
        };
      }
      if (!supplier && !partAInfo) {
        return {
          ...item
        };
      }
    });
  }

  async getContractName(
    contractIds: string[],
    reqUser: IReqUser,
    contract: any[]
  ) {
    const { orgId, tenantId } = reqUser;
    const contractList = await this.prisma.materialContract.findMany({
      where: {
        id: {
          in: contractIds
        },
        orgId,
        tenantId,
        isDeleted: false
      },
      select: {
        id: true,
        name: true
      }
    });
    return contract.map((item) => {
      const con = contractList.find((con) => con.id === item.contractId);
      return {
        ...item,
        contractName: con?.name
      };
    });
  }

  /**
   * 查询进场验收单下的供应商
   * @param reqUser
   * @param query
   * @returns
   */
  async getSupplierList(reqUser: IReqUser, query: QueryIncomingSupplierDto) {
    const { tenantId, orgId } = reqUser;
    const { contractId } = query;
    let supplier = await this.prisma.materialIncomingInspection.findMany({
      where: {
        orgId,
        tenantId,
        isDeleted: false,
        submitStatus: 'SUBMITTED',
        auditStatus: 'APPROVED',
        contractId: contractId ? contractId : undefined
      },
      distinct: ['supplierId'],
      select: {
        purchaseType: true,
        supplierId: true,
        contractId: true,
        supplierName: true,
        contractName: true
      }
    });
    const contractIds: string[] = supplier.map((item) => item.contractId!);
    const supplierIds: string[] = supplier.map((item) => item.supplierId!);
    // 获取合同名称
    supplier = await this.getContractName(contractIds, reqUser, supplier);
    supplier = await this.getSupplierNameForSelect(
      supplierIds,
      reqUser,
      supplier
    );
    // 排序将零星供应商放在最后面
    return supplier.sort((a, b) => {
      // 判断是否为零星供应商
      const isASporadic = a.supplierName === '零星材料供应商';
      const isBSporadic = b.supplierName === '零星材料供应商';

      // 如果A是零星而B不是，A排到后面
      if (isASporadic && !isBSporadic) return 1;

      // 如果B是零星而A不是，B排到后面
      if (!isASporadic && isBSporadic) return -1;

      // 如果两者都是或都不是，保持原有顺序
      return 0;
    });
  }

  /**
   * 新增收料单
   * @param reqUser
   * @returns
   */
  async add(req: Request, reqUser: IReqUser) {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;
    const day = currentDate.getDate();
    // 获取当前收料单的最新的收料单编号
    const code = await this.getMaxCode(reqUser, year, month);
    // 获取当前组织的名称
    const project = await this.platformService.getCurrentOrgInfo(
      req,
      reqUser.tenantId,
      reqUser.orgId
    );
    return await this.prisma.materialReceiving.create({
      data: {
        projectName: project.name || '',
        creator: reqUser.nickname,
        year: year,
        month: month,
        day: day,
        code: code,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }
    });
  }

  // 获取当前收料单的最新的收料单编号
  async getMaxCode(
    reqUser: IReqUser,
    year: number,
    month: number
  ): Promise<string> {
    const { tenantId, orgId } = reqUser;
    const code = ['收', `${year}${String(month).padStart(2, '0')}`, '001'];
    const maxCode = await this.prisma.materialReceiving.findFirst({
      where: {
        orgId,
        tenantId,
        isDeleted: false,
        year,
        month
      },
      orderBy: {
        code: 'desc'
      }
    });
    if (maxCode?.code.split('-')[1] === code[1]) {
      const lastCode = maxCode.code;
      const lastCodeNumber = parseInt(lastCode.split('-')[2], 10);
      code[2] = String(lastCodeNumber + 1).padStart(3, '0');
    }
    return code.join('-');
  }

  // 编辑收料单
  async update(
    id: string,
    reqUser: IReqUser,
    data: MaterialReceivingUpdateDto
  ) {
    await this.checkUpdateData(data, reqUser);
    // 查询单据收料时间
    const receiving = await this.getOne(id, reqUser);
    if (data.year && data.month && data.month !== receiving.month) {
      data.code = await this.getMaxCode(reqUser, data.year, data.month);
    }
    await this.prisma.materialReceiving.update({
      where: {
        id,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      },
      data: {
        ...data,
        updateBy: reqUser.id
      }
    });
    return true;
  }

  async checkUpdateData(data: MaterialReceivingUpdateDto, reqUser: IReqUser) {
    // 获取单据下的所有子项
    if (data.supplierId && data.year && data.month && data.day) {
      const salesDate = dayjs()
        .year(data.year)
        .month(data.month - 1)
        .date(data.day)
        .format('YYYY-MM-DD')
        .toString();
      // 查询最小时间
      const date = await this.materialReceivingDetailService.getEarliestTime(
        data.supplierId,
        data.contractId ?? null,
        reqUser
      );
      if (date) {
        const minDate = dayjs(date)
          .startOf('day')
          .format('YYYY-MM-DD')
          .toString();
        console.log(minDate, salesDate);
        if (salesDate < minDate)
          throw new BadRequestException(
            `收料日期不能大于进场最小日期，最小日期：${minDate}`
          );
        return;
      }
    }
  }

  async getDateTree(reqUser: IReqUser): Promise<TimeListResponseDto[]> {
    // 实现获取时间列表的逻辑
    const dates = await this.prisma.materialReceiving.findMany({
      distinct: ['year', 'month', 'day'],
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      },
      orderBy: [{ year: 'desc' }, { month: 'desc' }, { day: 'desc' }]
    });

    const resultMap: Record<string, TimeListResponseDto> = {};
    for (const time of dates) {
      // 添加父级，年_月
      if (!resultMap[`${time.year}_${time.month}`]) {
        resultMap[`${time.year}_${time.month}`] = {
          id: `${time.year}_${time.month}`,
          count: await this.getDateCount(reqUser, time.year, time.month),
          parentId: null,
          year: time.year,
          month: time.month
        };
      }

      // 添加子级 年_月_日
      resultMap[`${time.year}_${time.month}_${time.day}`] = {
        id: `${time.year}_${time.month}_${time.day}`,
        count: await this.getDateCount(
          reqUser,
          time.year,
          time.month,
          time.day
        ),
        parentId: `${time.year}_${time.month}`,
        year: time.year,
        month: time.month,
        day: time.day
      };
    }

    return Object.values(resultMap);
  }

  // 查询时间下的数据数量
  async getDateCount(
    reqUser: IReqUser,
    year: number,
    month: number,
    day?: number
  ): Promise<number> {
    return await this.prisma.materialReceiving.count({
      where: {
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false,
        year: year,
        month: month,
        day: day ? day : undefined
      }
    });
  }

  /**
   * 获取收料单列表
   * @param reqUser
   * @param query
   */
  async getList(reqUser: IReqUser, query: QueryMaterialReceivingDto) {
    const { onlyViewSelf = false } = query;
    let list = await this.prisma.$queryRaw<any[]>`
      with temp_material_categories as (
        select
          mrd.receiving_id,
          STRING_AGG(distinct
            case
              when position('|' in mdc.full_name) > 0
              then split_part(mdc.full_name, '|', 2)
              else split_part(mdc.full_name, '|', 1)
            end,
            ','
          ) filter (where mdc.full_name is not null) material_categories
        from material_receiving_detail mrd
        join material_dictionary_detail mdd
          on mdd.id = mrd.material_id
          and mdd.is_deleted = false
          and mdd.tenant_id = ${reqUser.tenantId}
        join material_dictionary_category mdc
          on mdc.id = mdd.material_dictionary_category_id
          and mdc.is_deleted = false
          and mdc.tenant_id = ${reqUser.tenantId}
          and mdc.org_id = mdd.org_id
        where mrd.is_deleted = false
          and mrd.tenant_id = ${reqUser.tenantId}
          and mrd.org_id = ${reqUser.orgId}
        group by mrd.receiving_id
      )
      select
        mr.material_settlement_status
        ,mr.id
        ,mr.code
        ,mr.purchase_type
        ,o.seal_name as project_name
        ,null as supplier_name
        ,mr.supplier_id
				,mr.contract_id
        ,mc.name as contract_name
        ,tmc.material_categories
				,mr.tax_excluded_amount
				,mr.tax_included_amount
        ,mr.supplier_name as old_supplier_name
        ,mr.creator
        ,mr.year
        ,mr.month
        ,mr.day
        ,mr.submit_status
        ,mr.audit_status
      from material_receiving mr
      left join temp_material_categories tmc
        on tmc.receiving_id = mr.id
      left join material_contract mc
        on mc.id = mr.contract_id
        and mc.tenant_id = mr.tenant_id
        and mc.org_id = mr.org_id
      left join platform_meta.org o
        on o.tenant_id = mr.tenant_id
        and o.id = mr.org_id
      where mr.tenant_id = ${reqUser.tenantId}
        and mr.org_id = ${reqUser.orgId}
        and mr.is_deleted = false
        ${onlyViewSelf ? Prisma.sql`and mr.create_by = ${reqUser.id}` : Prisma.empty}
        ${query.year ? Prisma.sql`and mr.year = ${query.year}` : Prisma.empty}
        ${query.month ? Prisma.sql`and mr.month = ${query.month}` : Prisma.empty}
        ${query.day ? Prisma.sql`and mr.day = ${query.day}` : Prisma.empty}
      order by mr.code desc, mr.id desc
    `;
    const supplierIds: string[] = list.map((item) => item.supplierId!);
    // 获取合同名称
    list = await this.getSupplierName(supplierIds, reqUser, list);
    return list;
  }

  /**
   * 获取单个数据
   * @param id
   * @param reqUser
   * @returns
   */
  async getOne(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;
    const materialReceiving = await this.prisma.materialReceiving.findUnique({
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      }
    });
    if (!materialReceiving) {
      throw new BadRequestException('收料单不存在');
    }
    return materialReceiving;
  }

  /**
   * 删除收料单
   * @param id
   * @param reqUser
   */
  async delete(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;
    // 删除前校验
    await this.beforeDelete(id, reqUser);
    await this.prisma.$transaction(async (tx) => {
      await tx.materialReceiving.update({
        where: {
          id,
          orgId,
          tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
      await tx.materialReceivingDetail.updateMany({
        where: {
          receivingId: id,
          orgId,
          tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
      await tx.materialReceivingIncomingDetail.updateMany({
        where: {
          receivingId: id,
          orgId,
          tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
      await tx.materialReceivingInventory.updateMany({
        where: {
          receivingId: id,
          orgId,
          tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
    });
    return true;
  }

  /**
   * 删除前校验
   * @param id
   * @param reqUser
   */
  async beforeDelete(id: string, reqUser: IReqUser) {
    // 查询收料单
    const materialReceiving = await this.getOne(id, reqUser);
    if (materialReceiving.submitStatus !== SubmitStatus.PENDING) {
      throw new BadRequestException('已提交的收料单，不能删除');
    }
  }

  /**
   * 修改提交状态
   * @param id
   * @param reqUser
   * @param data
   */
  async updateSubmitStatus(
    id: string,
    reqUser: IReqUser,
    body: MaterialReceivingUpdateSubmitStatusDto
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    const { submitStatus } = body;
    const data: {
      submitStatus: SubmitStatus;
      updateBy: string;
    } = {
      submitStatus,
      updateBy: userId
    };
    // 提交状态变更校验逻辑
    await this.beforeUpdateSubmitStatus(id, reqUser, submitStatus);
    await this.prisma.materialReceiving.update({
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      },
      data: data
    });
    return true;
  }

  /**
   * 提交状态变更校验逻辑
   * @param id
   * @param reqUser
   * @param submitStatus
   */
  async beforeUpdateSubmitStatus(
    id: string,
    reqUser: IReqUser,
    submitStatus: SubmitStatus
  ) {
    if (submitStatus === SubmitStatus.SUBMITTED) {
      // 提交状态校验
      await this.beforeSubmit(id, reqUser);
    } else {
      // 取消提交状态校验
      await this.beforeUnSubmit(id, reqUser);
    }
  }

  async beforeSubmit(id: string, reqUser: IReqUser) {
    // 查询收料单是否有明细
    const detail = await this.materialReceivingDetailService.getList(
      id,
      reqUser
    );
    const detailList = detail.list;
    if (!detailList.length) {
      throw new BadRequestException('收料单没有明细，不能提交');
    }
    // 查询收料单
    const materialReceiving = await this.getOne(id, reqUser);
    // 校验审批单的必填项
    if (materialReceiving.purchaseType !== PurchaseType.PARTY_A_SUPPLIED) {
      if (
        detailList.find(
          (item) =>
            item.priceExcludingTax === null || item.priceIncludingTax === null
        )
      ) {
        throw new BadRequestException(
          '存在明细的含税单价/不含税单价为空,请检查'
        );
      }
    }
  }

  async beforeUnSubmit(id: string, reqUser: IReqUser) {
    // 查询收料单
    const materialReceiving = await this.getOne(id, reqUser);
    if (materialReceiving.auditStatus === AuditStatus.APPROVED) {
      // 已审批不可取消提交
      throw new BadRequestException('已审批的收料单，不能取消提交');
    }
  }

  /**
   * 修改审批状态
   * @param id
   * @param reqUser
   * @param data
   */
  async updateAuditStatus(
    id: string,
    reqUser: IReqUser,
    auditStatus: AuditStatus
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    // 审批状态变更校验逻辑
    await this.beforeUpdateAuditStatus(id, reqUser, auditStatus);
    await this.prisma.materialReceiving.update({
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      },
      data: {
        auditStatus,
        updateBy: userId
      }
    });
    return true;
  }

  async beforeUpdateAuditStatus(
    id: string,
    reqUser: IReqUser,
    auditStatus: AuditStatus
  ) {
    // 查询收料单
    const { tenantId, orgId } = reqUser;
    const materialReceiving = await this.getOne(id, reqUser);
    if (
      (auditStatus === AuditStatus.PENDING ||
        auditStatus === AuditStatus.REJECTED) &&
      materialReceiving.auditStatus === AuditStatus.APPROVED
    ) {
      // 校验是否被领料单引用
      const requisitionData = await this.prisma.$queryRaw<any[]>`
          select mrfd.id from material_requisition_form_detail mrfd
          join material_receiving_inventory mri
            on mri.id = mrfd.material_receiving_inventory_id
            and mri.tenant_id = mrfd.tenant_id
            and mri.org_id = mrfd.org_id
            and mri.is_deleted = false
            and mri.receiving_id = ${id}
          join material_receiving mr
            on mr.id = mri.receiving_id
            and mr.tenant_id = mri.tenant_id
            and mr.org_id = mri.org_id
            and mr.is_deleted = false
          where mrfd.is_deleted = false
          and mrfd.org_id = ${orgId}
          and mrfd.tenant_id = ${tenantId}
        `;
      if (requisitionData.length) {
        throw new BadRequestException(
          `被领料单引用的收料单，不能${auditStatus === AuditStatus.PENDING ? '撤销' : '退回'}`
        );
      }
      // 校验是否被退货单引用
      const salesReturnData = await this.prisma.$queryRaw<any[]>`
        select mrfd.id from material_return_sales_form_detail mrfd
        join material_receiving_inventory mri
          on mri.id = mrfd.material_receiving_inventory_id
          and mri.tenant_id = mrfd.tenant_id
          and mri.org_id = mrfd.org_id
          and mri.is_deleted = false
          and mri.receiving_id = ${id}
        join material_receiving mr
          on mr.id = mri.receiving_id
          and mr.tenant_id = mri.tenant_id
          and mr.org_id = mri.org_id
          and mr.is_deleted = false
        where mrfd.is_deleted = false
        and mrfd.org_id = ${orgId}
        and mrfd.tenant_id = ${tenantId}
    `;
      if (salesReturnData.length) {
        throw new BadRequestException(
          `被退货单引用的收料单，不能${auditStatus === AuditStatus.PENDING ? '撤销' : '退回'}`
        );
      }
      // 校验是否被结算单引用
      const MaterialSettlementBillRefDetailData = await this.prisma.$queryRaw<
        any[]
      >`
        select mrfd.id from material_settlement_bill_ref_detail mrfd
        where mrfd.is_deleted = false
        and mrfd.settlement_bill_type::TEXT = ${SettlementBillType.RECEIVING}
        and mrfd.bill_id = ${id}
        and mrfd.org_id = ${orgId}
        and mrfd.tenant_id = ${tenantId}
    `;
      if (MaterialSettlementBillRefDetailData.length) {
        throw new BadRequestException(
          `被结算单引用的收料单，不能${auditStatus === AuditStatus.PENDING ? '撤销' : '退回'}`
        );
      }
      const materialAllocationRefDetailData = await this.prisma.$queryRaw<
        any[]
      >`
        select mrfd.id from material_allocation_from_detail mrfd
        join material_receiving_inventory mri
          on mri.id = mrfd.material_receiving_inventory_id
          and mri.tenant_id = mrfd.tenant_id
          and mri.org_id = mrfd.org_id
          and mri.is_deleted = false
          and mri.receiving_id = ${id}
        join material_receiving mr
          on mr.id = mri.receiving_id
          and mr.tenant_id = mri.tenant_id
          and mr.org_id = mri.org_id
          and mr.is_deleted = false
        where mrfd.is_deleted = false
        and mrfd.org_id = ${orgId}
        and mrfd.tenant_id = ${tenantId}
      `;
      if (materialAllocationRefDetailData.length) {
        throw new BadRequestException(
          `被调拨单引用的收料单，不能${auditStatus === AuditStatus.PENDING ? '撤销' : '退回'}`
        );
      }
    }
  }
}
