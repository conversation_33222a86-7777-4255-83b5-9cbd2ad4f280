import { ApiProperty, PickType } from '@nestjs/swagger';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString
} from 'class-validator';

import { DetailType, MaterialType } from '@/prisma/generated';

export class BaseMaterialReturnSalesFormDetailDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '父级id' })
  parentId?: string;

  @ApiProperty({ description: '退货单ID' })
  @IsNotEmpty({ message: '退货单ID不能为空' })
  @IsString({ message: '退货单ID必须是字符串' })
  returnSalesFormId: string;

  @ApiProperty({ description: '明细ID (收料单id)' })
  materialReceivingInventoryId?: string;

  @ApiProperty({ description: '明细的单据编码' })
  detailCode?: string;

  @ApiProperty({ description: '明细的单据时间' })
  detailDate?: string;

  @ApiProperty({ description: '材料ID' })
  @IsNotEmpty({ message: '材料ID不能为空' })
  @IsString({ message: '材料ID必须是字符串' })
  materialId: string;

  @ApiProperty({ description: '材料名称' })
  @IsNotEmpty({ message: '材料名称不能为空' })
  @IsString({ message: '材料名称必须是字符串' })
  materialName: string;

  @ApiProperty({ description: '材料规格' })
  @IsNotEmpty({ message: '材料规格不能为空' })
  @IsString({ message: '材料规格必须是字符串' })
  materialSpec: string;

  @ApiProperty({ description: '计量单位' })
  @IsOptional({ message: '计量单位可以为空' })
  @IsString({ message: '计量单位必须是字符串' })
  unit?: string | null;

  @ApiProperty({ description: '在库数量' })
  @IsOptional({ message: '在库数量可以为空' })
  @IsNumber({}, { message: '在库数量必须是数字' })
  inStockQuantity: number;

  @ApiProperty({ description: '在库单价' })
  @IsOptional({ message: '在库单价可以为空' })
  @IsString({ message: '在库单价必须是字符串' })
  inStockPrice: number;

  @ApiProperty({ description: '退货数量' })
  @IsNotEmpty({ message: '退货数量不可以为空' })
  @IsNumber({}, { message: '退货数量必须是数字' })
  salesReturnQuantity: number;

  @ApiProperty({ description: '退货单价' })
  @IsOptional({ message: '退货单价可以为空' })
  @IsNumber({}, { message: '退货单价必须是数字' })
  salesReturnPrice: number;

  @ApiProperty({ description: '退货金额' })
  @IsOptional({ message: '退货金额可以为空' })
  @IsNumber({}, { message: '退货金额必须是数字' })
  salesReturnAmount: number;

  @ApiProperty({ description: '排序号' })
  orderNo: number;

  @ApiProperty({ description: '备注' })
  @IsOptional({ message: '备注可以为空' })
  @IsString({ message: '备注必须是字符串' })
  remark?: string | null;
}

export class MaterialReturnSalesFormDetailResDto extends PickType(
  BaseMaterialReturnSalesFormDetailDto,
  [
    'id',
    'parentId',
    'returnSalesFormId',
    'materialReceivingInventoryId',
    'materialId',
    'materialName',
    'materialSpec',
    'unit',
    'orderNo',
    'inStockQuantity',
    'inStockPrice',
    'salesReturnQuantity',
    'salesReturnPrice',
    'salesReturnAmount',
    'remark'
  ] as const
) {}

export class MaterialReturnSalesFormDetailCreateDto {
  @ApiProperty({
    description: '数据数组'
  })
  @IsNotEmpty({ message: '数据数组不能为空' })
  @IsArray({ message: '数据数组必须为数组' })
  list: MaterialReturnSalesFormDetailCreateListDto[];

  @ApiProperty({
    description: '退货单id'
  })
  @IsNotEmpty({ message: '退货单id不能为空' })
  @IsString({ message: '退货单id格式不正确' })
  returnSalesFormId: string;
}

export class MaterialReturnSalesFormDetailCreateListDto extends PickType(
  BaseMaterialReturnSalesFormDetailDto,
  [
    'materialId',
    'materialName',
    'materialSpec',
    'unit',
    'inStockQuantity',
    'inStockPrice',
    'salesReturnQuantity',
    'remark',
    'orderNo'
  ] as const
) {}

export class MaterialReturnSalesFormDetailUpdateDto extends PickType(
  BaseMaterialReturnSalesFormDetailDto,
  ['salesReturnQuantity', 'remark'] as const
) {}

export class MaterialReturnSalesFormChooseCategoryTreeResDto {
  @ApiProperty({
    description: 'id'
  })
  id: string;

  @ApiProperty({
    description: '编码'
  })
  code: string;

  @ApiProperty({
    description: '类别名称'
  })
  name: string;

  @ApiProperty({
    description: '核算类型'
  })
  materialType: MaterialType;

  @ApiProperty({
    description: '备注'
  })
  remark: string;
}

export class MaterialReturnSalesFormChooseDetailsResDto {
  @ApiProperty({
    description: 'id'
  })
  id: string;

  @ApiProperty({
    description: '材料id'
  })
  materialId: string;

  @ApiProperty({
    description: '材料名称'
  })
  materialName: string;

  @ApiProperty({
    description: '材料规格型号'
  })
  materialSpec: string;

  @ApiProperty({
    description: '材料计量单位'
  })
  unit: string;

  @ApiProperty({
    description: '单价'
  })
  price: number;

  @ApiProperty({
    description: '库存数量'
  })
  inventoryQuantity: number;
}

export class MaterialReturnSalesFormChooseDetailsDto extends MaterialReturnSalesFormChooseDetailsResDto {
  @ApiProperty({
    description: '收料单id'
  })
  receivingId?: string;
}

export class MaterialReturnSalesFormChooseDetailsQueryDto {
  @ApiProperty({
    description: '退货单id'
  })
  @IsNotEmpty({ message: '退货单id不能为空' })
  @IsString({ message: '退货单id格式不正确' })
  returnSalesFormId: string;

  @ApiProperty({
    description: '分类id'
  })
  @IsOptional()
  @IsString({ message: '分类id格式不正确' })
  categoryId?: string;
}
