import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';
import { PlatformModule } from '@/modules/platform/platform.module';

import { ConcreteSubtotalAttachmentController } from './concrete-subtotal-attachment/concrete-subtotal-attachment.controller';
import { ConcreteSubtotalAttachmentService } from './concrete-subtotal-attachment/concrete-subtotal-attachment.service';

@Module({
  imports: [PrismaModule, PlatformModule],
  controllers: [ConcreteSubtotalAttachmentController],
  providers: [ConcreteSubtotalAttachmentService]
})
export class ConcreteSubtotalModule {}
