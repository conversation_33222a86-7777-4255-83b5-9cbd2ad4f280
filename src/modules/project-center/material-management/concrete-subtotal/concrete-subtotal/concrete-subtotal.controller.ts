// import {
//   Body,
//   Controller,
//   Delete,
//   Get,
//   Param,
//   Patch,
//   Post,
//   Query,
//   Req
// } from '@nestjs/common';
// import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

// import { ReqUser } from '@/common/decorators/req-user.decorator';
// import { TimeListResponseDto } from '@/common/dtos/common.dto';
// import { IReqUser } from '@/common/interfaces/req-user.interface';
// import { MaterialAllocationFrom } from '@/prisma/generated';

// import {
//   MaterialAllocationFormResDto,
//   MaterialAllocationFormUpdateAuditStatusDto,
//   MaterialAllocationFormUpdateDto,
//   MaterialAllocationFormUpdateSubmitStatusDto,
//   QueryMaterialAllocationFormDto
// } from './concrete-subtotal.dto';
// import { MaterialAllocationFromService } from './concrete-subtotal.service';

// @ApiTags('商品混凝土小票/单据')
// @Controller('concrete-subtotal')
// export class MaterialAllocationFromController {
//   constructor(private readonly service: MaterialAllocationFromService) {}

//   @ApiOperation({ summary: '新增商品混凝土小票' })
//   @ApiResponse({
//     status: 200,
//     description: '新增商品混凝土小票成功'
//   })
//   @Post()
//   async add(@ReqUser() reqUser: IReqUser): Promise<MaterialAllocationFrom> {
//     return await this.service.add(reqUser);
//   }

//   @ApiOperation({ summary: '编辑商品混凝土小票' })
//   @ApiResponse({
//     status: 200,
//     description: '编辑商品混凝土小票成功'
//   })
//   @Patch('/:id')
//   async update(
//     @Param('id') id: string,
//     @ReqUser() reqUser: IReqUser,
//     @Body() data: MaterialAllocationFormUpdateDto
//   ): Promise<boolean> {
//     return await this.service.update(id, reqUser, data);
//   }

//   @ApiOperation({ summary: '商品混凝土小票左侧树' })
//   @ApiResponse({
//     status: 200,
//     description: '获取商品混凝土小票左侧树成功',
//     type: TimeListResponseDto,
//     isArray: true
//   })
//   @Get('/date-tree')
//   async getDateTree(@ReqUser() reqUser: IReqUser) {
//     return await this.service.getDateTree(reqUser);
//   }

//   @ApiOperation({ summary: '商品混凝土小票/列表' })
//   @ApiResponse({
//     status: 200,
//     description: '获取商品混凝土小票/列表成功',
//     type: MaterialAllocationFormResDto,
//     isArray: true
//   })
//   @Get()
//   async getList(
//     @ReqUser() reqUser: IReqUser,
//     @Query() query: QueryMaterialAllocationFormDto
//   ) {
//     return await this.service.getList(reqUser, query);
//   }

//   @ApiOperation({ summary: '删除商品混凝土小票' })
//   @ApiResponse({
//     status: 200,
//     description: '删除商品混凝土小票成功'
//   })
//   @Delete('/:id')
//   async delete(
//     @ReqUser() reqUser: IReqUser,
//     @Param('id') id: string
//   ): Promise<boolean> {
//     return await this.service.delete(id, reqUser);
//   }

//   @ApiOperation({ summary: '提交状态变更' })
//   @ApiResponse({
//     status: 200,
//     description: '提交状态变更成功'
//   })
//   @Patch('/submit/:id')
//   async updateSubmitStatus(
//     @Param('id') id: string,
//     @ReqUser() reqUser: IReqUser,
//     @Body() body: MaterialAllocationFormUpdateSubmitStatusDto
//   ) {
//     return await this.service.updateSubmitStatus(id, reqUser, body);
//   }

//   @ApiOperation({ summary: '审核状态变更' })
//   @ApiResponse({
//     status: 200,
//     description: '审核状态变更成功'
//   })
//   @Patch('/audit/:id')
//   async updateAuditStatus(
//     @Param('id') id: string,
//     @ReqUser() reqUser: IReqUser,
//     @Body() body: MaterialAllocationFormUpdateAuditStatusDto
//   ) {
//     const { auditStatus } = body;
//     return await this.service.updateAuditStatus(id, reqUser, auditStatus);
//   }
// }
