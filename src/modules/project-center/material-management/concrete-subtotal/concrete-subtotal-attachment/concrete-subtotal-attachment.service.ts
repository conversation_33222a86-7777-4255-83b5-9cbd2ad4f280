import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import { ConcreteSubtotalAttachmentCreateDto } from './concrete-subtotal-attachment.dto';

@Injectable()
export class ConcreteSubtotalAttachmentService {
  constructor(private readonly prisma: PrismaService) {}

  async getList(concreteSubtotalOrderId: string, reqUser: IReqUser) {
    return await this.prisma.concreteSubtotalAttachment.findMany({
      where: {
        concreteSubtotalOrderId,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      }
    });
  }

  async add(data: ConcreteSubtotalAttachmentCreateDto, reqUser: IReqUser) {
    return await this.prisma.concreteSubtotalAttachment.create({
      data: {
        ...data,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        createBy: reqUser.id
      }
    });
  }

  async delete(id: string, reqUser: IReqUser) {
    return await this.prisma.concreteSubtotalAttachment.update({
      where: {
        id,
        isDeleted: false
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }
}
