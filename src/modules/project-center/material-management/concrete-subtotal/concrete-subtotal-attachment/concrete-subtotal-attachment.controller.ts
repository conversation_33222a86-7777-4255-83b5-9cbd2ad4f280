import { Body, Controller, Delete, Get, Param, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  BaseConcreteSubtotalAttachmentDto,
  ConcreteSubtotalAttachmentResDto
} from './concrete-subtotal-attachment.dto';
import { ConcreteSubtotalAttachmentService } from './concrete-subtotal-attachment.service';

@ApiTags('商品混凝土小计/附件')
@Controller('concrete-subtotal-attachment')
export class ConcreteSubtotalAttachmentController {
  constructor(private readonly service: ConcreteSubtotalAttachmentService) {}

  @ApiOperation({
    summary: '获取附件列表',
    description: '获取附件列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取附件列表成功',
    type: ConcreteSubtotalAttachmentResDto,
    isArray: true
  })
  @Get('/:concreteSubtotalOrderId')
  async getList(
    @Param('concreteSubtotalOrderId') concreteSubtotalOrderId: string,
    @ReqUser() reqUser: IReqUser
  ) {
    return await this.service.getList(concreteSubtotalOrderId, reqUser);
  }

  @ApiOperation({
    summary: '新增附件列表',
    description: '新增附件列表'
  })
  @ApiResponse({
    status: 200,
    description: '新增附件列表成功'
  })
  @Post()
  async add(
    @ReqUser() reqUser: IReqUser,
    @Body() data: BaseConcreteSubtotalAttachmentDto
  ) {
    return await this.service.add(data, reqUser);
  }

  @ApiOperation({
    summary: '删除附件列表',
    description: '删除附件列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取附件列表成功'
  })
  @Delete('/:id')
  async delete(@ReqUser() reqUser: IReqUser, @Param('id') id: string) {
    return await this.service.delete(id, reqUser);
  }
}
