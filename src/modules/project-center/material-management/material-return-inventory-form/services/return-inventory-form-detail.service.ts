import {
  BadRequestException,
  Injectable,
  NotFoundException
} from '@nestjs/common';
import { Decimal } from '@prisma/client/runtime/library';
import * as dayjs from 'dayjs';
import * as uuid from 'uuid';

import { CommonRepositories } from '@/common/common-repositories';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import {
  MaterialReturnInventoryForm,
  MaterialReturnInventoryFormDetail
} from '@/prisma/generated';

import {
  MaterialReturnInventoryFormChooseCategoryQueryDto,
  MaterialReturnInventoryFormChooseCategoryTreeResDto,
  MaterialReturnInventoryFormChooseDetailsQueryDto,
  MaterialReturnInventoryFormChooseDetailsResDto,
  MaterialReturnInventoryFormDetailCreateDto,
  MaterialReturnInventoryFormDetailCreateListDto,
  MaterialReturnInventoryFormDetailResDto,
  MaterialReturnInventoryFormDetailUpdateDto
} from '../material-return-inventory-form.dto';
import { ReturnInventoryFormDetailRepository } from '../repositories/return-inventory-form-detail.repository';

interface Material {
  id: string;
  materialId: string;
  materialName: string;
  materialSpec: string;
  unit: string;
  canReversalQuantity: number;
}

interface MaterialRecord {
  actualInventoryQuantity: number;
  price: number;
  materialRequisitionDetailId: string;
  businessCostSubjectDetailId: string;
  partName: string;
  materialReceivingId: string;
  unit: string;
  code: string;
  materialId: string;
  materialName: string;
  materialSpec: string;
  createAt: string;
}
@Injectable()
export class ReturnInventoryFormDetailService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly repository: ReturnInventoryFormDetailRepository
  ) {}

  /**
   * 查询可选的材料分类
   * @param returnSalesFormId
   * @param reqUser
   */
  async getChooseMaterialCategory(
    reqUser: IReqUser,
    query: MaterialReturnInventoryFormChooseCategoryQueryDto
  ): Promise<MaterialReturnInventoryFormChooseCategoryTreeResDto[]> {
    // 根据退库单id查询该退库单
    const returnSalesForm = await this.getOne(
      query.materialReversalId,
      reqUser
    );
    const returnSalesFormDate =
      returnSalesForm.year + '-' + returnSalesForm.month + returnSalesForm.day;
    const materialCategoryList: MaterialReturnInventoryFormChooseCategoryTreeResDto[] =
      await this.repository.getChooseMaterialCategory(
        returnSalesFormDate,
        returnSalesForm.supplierId,
        returnSalesForm.returnInventoryType,
        reqUser
      );
    return materialCategoryList;
  }

  /**
   * 查询可选的材料明细
   * @param returnSalesFormId
   * @param reqUser
   */
  async getChooseMaterialDetails(
    query: MaterialReturnInventoryFormChooseDetailsQueryDto,
    reqUser: IReqUser
  ): Promise<MaterialReturnInventoryFormChooseDetailsResDto[]> {
    // 根据退库单id查询该退库单
    const returnSalesForm = await this.getOne(
      query.materialReversalId,
      reqUser
    );
    const returnSalesFormDate =
      returnSalesForm.year +
      '-' +
      returnSalesForm.month +
      '-' +
      returnSalesForm.day;
    const materialDetailsList: MaterialReturnInventoryFormChooseDetailsResDto[] =
      await this.repository.getChooseMaterialDetails(
        returnSalesFormDate,
        returnSalesForm?.supplierId,
        query.materialReversalId,
        returnSalesForm.returnInventoryType,
        reqUser,
        query.categoryId
      );
    return await this.mergeMaterials(materialDetailsList);
  }

  async add(
    body: MaterialReturnInventoryFormDetailCreateDto,
    reqUser: IReqUser
  ): Promise<boolean> {
    const { materialReversalId, list } = body;
    // 退库要新增的数据
    const addList: any[] = [];
    // 领料修改库存
    const updateRequisitionList: any[] = [];
    // 获取单据数据
    const form = await this.getOne(materialReversalId, reqUser);
    const returnSalesFormDate = form.year + '-' + form.month + '-' + form.day;
    // 查询所有涉及到的材料的领料
    const requisitionAndReturnList =
      await this.repository.getMaterialRequisitionData(
        returnSalesFormDate,
        reqUser,
        list,
        form?.supplierId
      );
    // 将数据进行map重组再排序
    const materialMap = await this.groupAndSortMaterials(
      requisitionAndReturnList,
      list
    );
    // 判断父级是否存在
    for (const element of materialMap.keys()) {
      const obj = await this.notHasParent(
        reqUser,
        materialReversalId,
        element,
        materialMap.get(element) || []
      );
      // 数据计算
      const list = await this.calculation(obj.addList);
      addList.push(...list);
      updateRequisitionList.push(...obj.updateRequisitionList);
    }
    // 拿到领料的库存去寻找收料的库存数据
    const receivingAndReturnList = await this.getReceivingAndReturnList(
      reqUser,
      updateRequisitionList
    );
    await this.prisma.$transaction(async (tx) => {
      // 新增数据
      await tx.materialReturnInventoryFormDetail.createMany({
        data: addList
      });
      // 领料的库存扣减
      for (const element of updateRequisitionList) {
        await tx.materialRequisitionFormDetail.update({
          where: {
            id: element.materialRequisitionDetailId
          },
          data: {
            actualInventoryQuantity: {
              decrement: Decimal(element.quantity)
            }
          }
        });
      }
      // 收料库存增加
      for (const element of receivingAndReturnList) {
        await tx.materialReceivingInventory.update({
          where: {
            id: element.id as string
          },
          data: {
            requisitionQuantity: {
              decrement: Decimal(element.returnQuantity || 0)
            },
            inventoryQuantity: {
              increment: Decimal(element.returnQuantity || 0)
            }
          }
        });
      }
    });
    // 计算并统计金额写入单据
    // 查询单据下所有的明细
    const receivingAmount =
      await this.prisma.materialReturnInventoryFormDetail.aggregate({
        where: {
          materialReversalId,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          isDeleted: false,
          parentId: null
        },
        _sum: {
          reversalAmount: true
        }
      });
    await this.prisma.materialReturnInventoryForm.update({
      where: {
        id: materialReversalId,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      },
      data: {
        amount: receivingAmount._sum.reversalAmount,
        updateBy: reqUser.id
      }
    });
    return true;
  }

  async calculation(addList: any[]) {
    const list: any[] = [];
    const parent: any = addList.find(
      (element) => element.parentId === null || !element.parentId
    );
    const childList = addList.filter((element) => {
      return element.parentId;
    });
    // 同批总金额
    const totalAmount = childList.reduce(
      (pre: Decimal, cur) =>
        pre.add(Decimal(cur.reversalPrice).mul(cur.reversalQuantity)),
      new Decimal(0)
    );
    // 计算子级退货数量之和
    const totalQuantity = childList.reduce(
      (pre: Decimal, cur) => pre.add(Decimal(cur.reversalQuantity)),
      new Decimal(0)
    );
    // 如果子级退货数量相加等于父级
    let notLastAmount = new Decimal(0);
    if (parent.reversalQuantity === totalQuantity.toNumber()) {
      // 说明存在最后一批退货
      // 计算除最后一批退货的金额
      childList.forEach((item, index) => {
        if (index !== 0) {
          notLastAmount = notLastAmount.add(
            Decimal(item.reversalPrice).mul(item.reversalQuantity)
          );
        }
      });
    }
    // 最后一批的金额
    childList.forEach((element, index) => {
      if (index === 0) {
        // 最后一批
        element.reversalPrice = element.requisitionPrice;
        element.reversalAmount = Decimal(totalAmount.sub(notLastAmount));
      } else {
        // 不是最后一批
        element.reversalPrice = element.requisitionPrice;
        element.reversalAmount = Decimal(element.reversalPrice).mul(
          element.requisitionPrice
        );
      }
      list.push(element);
    });
    // 计算父级
    parent.reversalAmount = list.reduce(
      (pre, cur) => pre.add(cur.reversalAmount),
      new Decimal(0)
    );
    parent.reversalPrice = parent.reversalAmount.div(parent.reversalQuantity);
    list.unshift(parent);
    return list;
  }

  // 查询退货库下领料明细的最早时间
  async getEarliestTime(
    supplierId: string,
    reqUser: IReqUser
  ): Promise<string | null> {
    return await this.repository.getEarliestTime(supplierId, reqUser);
  }

  // 拿到领料的数据去寻找收料的库存数据
  async getReceivingAndReturnList(
    reqUser: IReqUser,
    updateRequisitionList: any[]
  ) {
    const returnList = [];
    const ids = updateRequisitionList.map(
      (item) => item.materialRequisitionDetailId
    );
    const list = await this.prisma.materialRequisitionFormDetail.findMany({
      where: {
        parentId: {
          in: ids
        },
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId
      },
      select: {
        parentId: true,
        actualQuantity: true,
        actualInventoryQuantity: true,
        materialReceivingInventoryId: true
      },
      orderBy: {
        orderNo: 'desc'
      }
    });
    // 2. 按parentId分组（key: materialRequisitionDetailId）
    const grouped = list.reduce(
      (acc, item) => {
        const key = item.parentId;
        if (!acc[key as string]) {
          acc[key as string] = [];
        }
        acc[key as string].push(item);
        return acc;
      },
      {} as Record<string, (typeof list)[0][]>
    );
    // 3. 遍历updateRequisitionList，处理每个条目
    for (const updateItem of updateRequisitionList) {
      const { materialRequisitionDetailId: parentId, quantity } = updateItem;
      const details = grouped[parentId]; // 获取对应分组的明细

      if (!details || details.length === 0) continue;

      let remainingQuantity = Number(quantity); // 剩余需要递减的数量

      // 按orderNo倒序遍历明细（已在查询时排序，直接遍历即可）
      for (const detail of details) {
        if (remainingQuantity <= 0) break; // 数量已减完，退出循环

        // 计算本次需要递减的数量（不超过当前actualQuantity）
        const decrement = Math.min(
          remainingQuantity,
          Decimal(detail.actualQuantity || 0).toNumber()
        );

        // 执行递减（实际业务中可能需要更新数据库）
        remainingQuantity -= decrement;
        returnList.push({
          id: detail.materialReceivingInventoryId,
          returnQuantity:
            Decimal(detail.actualQuantity || 0).toNumber() > decrement
              ? decrement
              : detail.actualQuantity
        });
      }
    }

    return returnList; // 返回处理后的列表
  }

  async getDetailList(
    id: string,
    reqUser: IReqUser
  ): Promise<MaterialReturnInventoryFormDetailResDto[]> {
    return await this.repository.getList(id, reqUser);
  }

  async update(
    id: string,
    body: MaterialReturnInventoryFormDetailUpdateDto,
    reqUser: IReqUser
  ) {
    // 校验明细
    const detail =
      await this.prisma.materialReturnInventoryFormDetail.findUnique({
        where: {
          id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        }
      });
    if (!detail) {
      throw new NotFoundException('明细不存在');
    }
    if (
      !body.reversalQuantity ||
      Decimal(body.reversalQuantity).equals(
        Decimal(detail.reversalQuantity || 0)
      )
    ) {
      // 如果数据一致，则不处理，更新备注和折旧摊销金额
      await this.updateRemarkAndDepreciationAmount(id, detail, reqUser, body);
      return true;
    }
    // 查询单据
    const returnSalesForm = await this.getOne(
      detail.materialReversalId,
      reqUser
    );
    const param: any = {
      supplierId: returnSalesForm?.supplierId,
      materialName: detail.materialName,
      materialSpec: detail.materialSpec,
      unit: detail.unit
    };
    // 校验退库数量
    await this.checkReturnQuantity(
      param,
      Decimal(body.reversalQuantity),
      detail.reversalQuantity || null,
      reqUser
    );
    // 删除现有的明细
    await this.delete(id, reqUser);
    // 查询现有库存数量
    const canReversalQuantity = await this.repository.getInventory(
      param,
      reqUser
    );
    // 重新创建
    const materialReturnInventoryFormDetailCreateDto: MaterialReturnInventoryFormDetailCreateDto =
      {
        list: [
          {
            materialId: detail.materialId,
            materialName: detail.materialName || '',
            materialSpec: detail.materialSpec || '',
            unit: detail.unit || '',
            canReversalQuantity,
            reversalQuantity: body.reversalQuantity,
            remark: body.remark,
            orderNo: detail.orderNo
          }
        ],
        materialReversalId: detail.materialReversalId
      };
    await this.add(materialReturnInventoryFormDetailCreateDto, reqUser);
    return true;
  }

  async delete(id: string, reqUser: IReqUser) {
    // 校验父级
    const parentDetail = await this.getOneDetail(id, reqUser);
    // 查询改明细下的所有子级
    const children = await this.getDetailChildren(id, reqUser);
    const ids: string[] = [];
    children.forEach((item) => {
      ids.push(item.id);
    });
    ids.push(id);
    // 拿到领料的库存去寻找收料的库存数据
    const receivingAndReturnList = await this.getReceivingAndReturnList(
      reqUser,
      children
    );
    // 然后进行库存返回
    await this.prisma.$transaction(async (tx) => {
      // 先返还领料库存
      for (const element of children) {
        await tx.materialRequisitionFormDetail.update({
          where: {
            id: element.materialRequisitionDetailId as string
          },
          data: {
            actualInventoryQuantity: {
              increment: Decimal(element.quantity || 0)
            }
          }
        });
      }
      // 再返还收料库存
      // 收料库存增加
      for (const element of receivingAndReturnList) {
        await tx.materialReceivingInventory.update({
          where: {
            id: element.id as string
          },
          data: {
            requisitionQuantity: {
              increment: Decimal(element.returnQuantity || 0)
            },
            inventoryQuantity: {
              decrement: Decimal(element.returnQuantity || 0)
            }
          }
        });
      }
      await tx.materialReturnInventoryFormDetail.updateMany({
        where: {
          id: {
            in: ids
          }
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
      await tx.materialReturnInventoryForm.update({
        where: {
          id: parentDetail.materialReversalId
        },
        data: {
          amount: {
            decrement: Decimal(parentDetail.reversalAmount || 0)
          }
        }
      });
    });
    return true;
  }

  async move(reqUser: IReqUser, fromId: string, toId: string) {
    await CommonRepositories.changeDataOrderNo(this.prisma, {
      tenantId: reqUser.tenantId,
      orgId: reqUser.orgId,
      fromId,
      toId,
      tableName: 'material_return_inventory_form_detail'
    });
    return true;
  }

  // 查询所有的父级明细
  async getParentList(materialReversalId: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    return await this.prisma.materialReturnInventoryFormDetail.findMany({
      where: {
        materialReversalId,
        parentId: null,
        tenantId,
        orgId,
        isDeleted: false
      }
    });
  }

  /**
   * 获取退库单下所有明细（子级）
   * @param id
   * @param reqUser
   * @returns
   */
  async getList(id: string, reqUser: IReqUser) {
    return await this.prisma.materialReturnInventoryFormDetail.findMany({
      where: {
        parentId: null,
        materialReversalId: id,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      }
    });
  }

  /**
   * 查询单个退货单详情
   * @param id
   * @param reqUser
   * @returns
   */
  async getOneDetail(id: string, reqUser: IReqUser) {
    const returnSalesFormDetail =
      await this.prisma.materialReturnInventoryFormDetail.findUnique({
        where: {
          id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        }
      });
    if (!returnSalesFormDetail) {
      throw new NotFoundException('未找到该退货单明细');
    }
    if (returnSalesFormDetail.parentId) {
      throw new BadRequestException(`只有父级可以操作`);
    }
    return returnSalesFormDetail;
  }

  /**
   * 查询父级明细下的所有子级
   * @param id
   * @param reqUser
   * @returns
   */
  async getDetailChildren(id: string, reqUser: IReqUser) {
    return await this.prisma.$queryRaw<any[]>`
      select 
        id
        ,material_requisition_detail_id
        ,reversal_inventory_quantity as quantity
      from material_return_inventory_form_detail
      where parent_id = ${id}
      and is_deleted = false
      and tenant_id = ${reqUser.tenantId}
      and org_id = ${reqUser.orgId}
    `;
  }

  // 如果数据一致，则不处理，更新备注和折旧摊销金额
  async updateRemarkAndDepreciationAmount(
    id: string,
    detail: MaterialReturnInventoryFormDetail,
    reqUser: IReqUser,
    body: MaterialReturnInventoryFormDetailUpdateDto
  ) {
    await this.prisma.$transaction(async (prisma) => {
      if (!detail.parentId) {
        // 父级修改且退货数量与原先一致则只更新备注
        await prisma.materialReturnInventoryFormDetail.update({
          where: {
            id: id,
            tenantId: reqUser.tenantId,
            orgId: reqUser.orgId,
            isDeleted: false
          },
          data: {
            remark: body.remark,
            updateBy: reqUser.id
          }
        });
      } else {
        // 计算折旧摊销金额的差值
        const depreciationAmount = Decimal(body.depreciationAmount || 0).minus(
          detail.depreciationAmount || 0
        );
        // 子级修改则更新折旧摊销金额和备注
        await prisma.materialReturnInventoryFormDetail.update({
          where: {
            id: id,
            tenantId: reqUser.tenantId,
            orgId: reqUser.orgId,
            isDeleted: false
          },
          data: {
            remark: body.remark,
            depreciationAmount: body.depreciationAmount
              ? Decimal(body.depreciationAmount)
              : null,
            reversalAmount: Decimal(detail.reversalAmount || 0).minus(
              depreciationAmount || 0
            ),
            updateBy: reqUser.id
          }
        });
        // 变更父级的金额
        const reversalAmount = Decimal(detail.reversalAmount || 0).minus(
          Decimal(depreciationAmount || 0)
        );
        await prisma.materialReturnInventoryFormDetail.update({
          where: {
            id: detail.parentId,
            tenantId: reqUser.tenantId,
            orgId: reqUser.orgId,
            isDeleted: false
          },
          data: {
            depreciationAmount: Decimal(detail.depreciationAmount || 0).add(
              Decimal(depreciationAmount || 0)
            ),
            reversalAmount,
            reversalPrice: Decimal(reversalAmount || 0).div(
              Decimal(detail.reversalQuantity || 0)
            ),
            updateBy: reqUser.id
          }
        });
        // 修改单据的金额
        await prisma.materialReturnInventoryForm.update({
          where: {
            id: detail.materialReversalId,
            tenantId: reqUser.tenantId,
            orgId: reqUser.orgId,
            isDeleted: false
          },
          data: {
            amount: {
              decrement: Decimal(depreciationAmount || 0)
            },
            updateBy: reqUser.id
          }
        });
      }
    });
  }

  /**
   * 校验退库数量
   */
  async checkReturnQuantity(
    data: any,
    reversalQuantity: Decimal,
    parentReversalQuantity: Decimal | null,
    reqUser: IReqUser
  ) {
    // 查询现有库存数量
    const inventoryQuantity = Decimal(
      await this.repository.getInventory(data, reqUser)
    );
    // 该退库单明细现存退库数量
    const existingSalesReturnQuantity = Decimal(parentReversalQuantity || 0);
    // 要变更的库存
    const updateSalesReturnQuantity = Decimal(reversalQuantity);
    if (
      existingSalesReturnQuantity.toNumber() <
      updateSalesReturnQuantity.toNumber()
    ) {
      // 要补的退库数量
      const addSalesReturnQuantity = updateSalesReturnQuantity.minus(
        existingSalesReturnQuantity
      );
      if (inventoryQuantity.toNumber() < addSalesReturnQuantity.toNumber()) {
        // 现有库存不足，无法退
        throw new BadRequestException('当前库存数量不足，请重新输入退库数量');
      }
    }
  }

  /**
   * 父级不存在
   * @param data 数据
   * @param reqUser
   * @param materialReversalId 退库单id
   */
  async notHasParent(
    reqUser: IReqUser,
    materialReversalId: string,
    materialData: MaterialReturnInventoryFormDetailCreateListDto,
    data: MaterialRecord[]
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    const parentId = uuid.v7();
    // 新增的退库数组
    const addList = [];
    // 领料修改库存
    const updateRequisitionList: any[] = [];
    // 创建父级
    const parent = {
      materialReversalId,
      id: parentId,
      orgId,
      tenantId,
      materialId: materialData.materialId,
      materialName: materialData.materialName,
      materialSpec: materialData.materialSpec,
      unit: materialData.unit,
      partName: null,
      businessCostSubjectDetailId: null,
      canReversalQuantity: materialData.canReversalQuantity,
      reversalQuantity: materialData.reversalQuantity,
      reversalPrice: Decimal(0),
      reversalAmount: Decimal(0),
      reversalInventoryQuantity: materialData.reversalQuantity,
      createBy: userId,
      updateBy: userId,
      remark: materialData.remark,
      orderNo: materialData.orderNo
    };
    // 要退的数量
    let quantity = materialData.reversalQuantity || 0;
    // 判断子级
    // 使用部位的集合
    const partNameList = new Set<string>();
    // 成本科目的集合
    const costSubjectList = new Set<string>();
    for (const element of data) {
      // 要退的数量必须要大于0，小于0后停止
      if (quantity > 0) {
        partNameList.add(element.partName);
        costSubjectList.add(element.businessCostSubjectDetailId);
        // 组装数据
        addList.push({
          orgId,
          tenantId,
          createBy: userId,
          updateBy: userId,
          parentId,
          materialReversalId,
          materialId: element.materialId,
          materialName: element.code,
          materialReceivingId: element.materialReceivingId,
          materialSpec: dayjs(element.createAt).format('YYYY-MM-DD'),
          unit: element.unit,
          materialRequisitionDetailId: element.materialRequisitionDetailId,
          partName: element.partName,
          businessCostSubjectDetailId: element.businessCostSubjectDetailId,
          requisitionPrice: element.price,
          canReversalQuantity: element.actualInventoryQuantity,
          reversalQuantity:
            quantity > element.actualInventoryQuantity
              ? element.actualInventoryQuantity
              : quantity,
          reversalPrice: element.price,
          reversalInventoryQuantity:
            quantity > element.actualInventoryQuantity
              ? element.actualInventoryQuantity
              : quantity,
          reversalAmount: Decimal(element.price).mul(
            quantity > element.actualInventoryQuantity
              ? element.actualInventoryQuantity
              : quantity
          )
        });
        // 领料库存修改新增
        updateRequisitionList.push({
          materialRequisitionDetailId: element.materialRequisitionDetailId,
          quantity:
            quantity > element.actualInventoryQuantity
              ? element.actualInventoryQuantity
              : quantity
        });
        quantity -= element.actualInventoryQuantity;
      }
    }
    if (partNameList.size === 1) {
      parent.partName = partNameList.values().next().value;
    }
    parent.reversalAmount = addList.reduce(
      (acc, cur) => acc.add(Decimal(cur.reversalAmount)),
      Decimal(0)
    );
    parent.reversalPrice = Decimal(parent.reversalAmount).div(
      Decimal(parent.reversalQuantity)
    );
    if (costSubjectList.size === 1) {
      parent.businessCostSubjectDetailId = costSubjectList
        .values()
        .next().value;
    }
    addList.unshift(parent);
    return {
      addList,
      updateRequisitionList
    };
  }

  // 将数据按照材料名称+材料规格+单位进行map重组再排序
  async groupAndSortMaterials(
    records: MaterialRecord[],
    parent: MaterialReturnInventoryFormDetailCreateListDto[]
  ): Promise<
    Map<MaterialReturnInventoryFormDetailCreateListDto, MaterialRecord[]>
  > {
    // 创建一个映射表，键为 materialName、materialSpec 和 price 的组合
    const materialMap = new Map<
      MaterialReturnInventoryFormDetailCreateListDto,
      MaterialRecord[]
    >();

    parent.forEach((item) => {
      materialMap.set(item, []);
    });

    // 按 materialName、materialSpec 和 price 分组
    for (const element of materialMap.keys()) {
      records.forEach((record) => {
        if (
          element.materialId === record.materialId &&
          element.materialName === record.materialName &&
          element.materialSpec === record.materialSpec &&
          element.unit === record.unit
        ) {
          materialMap.get(element)!.push(record);
        }
      });
    }

    // 对每个分组内的记录进行排序
    materialMap.forEach((group) => {
      group.sort((a, b) => {
        // 先比较 createAt（降序）
        const dateA = new Date(a.createAt);
        const dateB = new Date(b.createAt);
        const dateDiff = dateB.getTime() - dateA.getTime();
        return dateDiff; // 日期不同时，按日期降序
      });
    });

    return materialMap;
  }

  // 数据合并
  private async mergeMaterials(
    materials: MaterialReturnInventoryFormChooseDetailsResDto[]
  ): Promise<Material[]> {
    // 创建一个映射表，键为组合的唯一标识，值为合并后的材料对象
    const mergedMap = new Map<string, Material>();

    materials.forEach((material) => {
      // 创建基于 material_id、material_name、material_spec、unit 和 price 的唯一键
      const key = `${material.materialId}-${material.materialName}-${material.materialSpec}-${material.unit}`;

      if (mergedMap.has(key)) {
        // 如果已存在相同组合的材料，则累加 inventory_quantity
        const existingMaterial = mergedMap.get(key)!;
        const total = new Decimal(existingMaterial.canReversalQuantity).plus(
          material.canReversalQuantity
        ); // 四舍五入保留8位
        existingMaterial.canReversalQuantity = Number(total);
      } else {
        // 如果不存在，则添加新的材料记录
        mergedMap.set(key, { ...material });
      }
    });

    // 将映射表中的值转换为数组返回
    return Array.from(mergedMap.values());
  }

  /**
   * 查询单个退库单
   * @param id
   * @param reqUser
   * @returns
   */
  async getOne(
    id: string,
    reqUser: IReqUser
  ): Promise<MaterialReturnInventoryForm> {
    const returnSalesForm =
      await this.prisma.materialReturnInventoryForm.findUnique({
        where: {
          id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        }
      });
    if (!returnSalesForm) {
      throw new NotFoundException('未找到该退库单');
    }
    return returnSalesForm;
  }
}
