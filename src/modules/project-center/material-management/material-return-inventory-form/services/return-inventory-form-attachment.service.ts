import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import {
  MaterialReturnInventoryAttachmentCreateDto,
  MaterialReturnInventoryAttachmentResDto
} from '../material-return-inventory-form.dto';

@Injectable()
export class ReturnInventoryFormAttachmentService {
  constructor(private readonly prisma: PrismaService) {}

  // 新增附件
  async add(
    reqUser: IReqUser,
    data: MaterialReturnInventoryAttachmentCreateDto
  ) {
    await this.prisma.materialReturnInventoryFormAttachment.create({
      data: {
        ...data,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }
    });
    return true;
  }

  // 获取附件列表
  async getList(
    materialReversalId: string,
    reqUser: IReqUser
  ): Promise<MaterialReturnInventoryAttachmentResDto[]> {
    const result =
      await this.prisma.materialReturnInventoryFormAttachment.findMany({
        where: {
          materialReversalId,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        },
        select: {
          id: true,
          fileName: true,
          fileKey: true,
          fileSize: true,
          fileExt: true,
          fileContentType: true
        }
      });

    return result;
  }

  // 删除附件
  async delete(id: string, reqUser: IReqUser) {
    await this.prisma.materialReturnInventoryFormAttachment.update({
      where: {
        id,
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
    return true;
  }
}
