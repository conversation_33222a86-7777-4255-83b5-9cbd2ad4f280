import { ApiProperty, PickType } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString
} from 'class-validator';

import {
  AuditStatus,
  MaterialSettlementStatus,
  MaterialType,
  ReturnInventoryType,
  SubmitStatus
} from '@/prisma/generated';

// 单据
export class BaseMaterialReturnInventoryFormDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '结转状态' })
  settleStatus: MaterialSettlementStatus;

  @ApiProperty({ description: '项目名称' })
  projectName?: string;

  @ApiProperty({ description: '单据编码' })
  @IsNotEmpty({ message: '单据编码不能为空' })
  @IsString({ message: '单据编码必须是字符串' })
  code: string;

  @ApiProperty({ description: '退库类型' })
  @IsNotEmpty({ message: '退库类型不能为空' })
  @IsIn(Object.values(ReturnInventoryType), {
    message: '退库类型必须是有效枚举值'
  })
  @IsString({ message: '退库类型必须是字符串' })
  returnInventoryType: ReturnInventoryType;

  @ApiProperty({ description: '退料单位ID：供应商ID' })
  @IsOptional({ message: '退料单位ID：供应商ID不能为空' })
  @IsString({ message: '退料单位ID：供应商ID必须是字符串' })
  supplierId?: string | null;

  @ApiProperty({ description: '材料类别' })
  materialCategories: string;

  @ApiProperty({ description: '供应商名称' })
  @IsOptional({ message: '供应商名称不能为空' })
  @IsString({ message: '供应商名称必须是字符串' })
  supplierName?: string | null;

  @ApiProperty({ description: '金额' })
  amount?: number;

  @ApiProperty({ description: '提交状态' })
  @IsNotEmpty({ message: '提交状态不能为空' })
  @IsIn(Object.values(SubmitStatus), {
    message: '提交状态必须是有效枚举值'
  })
  @IsString({ message: '提交状态必须是字符串' })
  submitStatus: SubmitStatus;

  @ApiProperty({ description: '审批状态' })
  @IsNotEmpty({ message: '审批状态不能为空' })
  @IsIn(Object.values(AuditStatus), {
    message: '审批状态必须是有效枚举值'
  })
  @IsString({ message: '审批状态必须是字符串' })
  auditStatus: AuditStatus;

  @ApiProperty({ description: '年' })
  @IsOptional({ message: '年可以为空' })
  @Type(() => Number)
  @IsNumber({}, { message: '年必须是数字' })
  year?: number;

  @ApiProperty({ description: '月' })
  @IsOptional({ message: '月可以为空' })
  @Type(() => Number)
  @IsNumber({}, { message: '月必须是数字' })
  month?: number;

  @ApiProperty({ description: '日' })
  @IsOptional({ message: '日可以为空' })
  @Type(() => Number)
  @IsNumber({}, { message: '日必须是数字' })
  day?: number;

  @ApiProperty({ description: '创建人名称' })
  creator: string;

  @ApiProperty({ description: '编制时间' })
  createAt: Date;

  @ApiProperty({ description: '旧的供应商名称' })
  oldSupplierName: string;
}

export class QueryMaterialReturnInventoryFormDto extends PickType(
  BaseMaterialReturnInventoryFormDto,
  ['year', 'month', 'day']
) {
  @ApiProperty({ description: '是否仅查看自己的数据,默认为false' })
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  onlyViewSelf: boolean;
}

export class MaterialReturnInventoryFormSupplierResDto extends PickType(
  BaseMaterialReturnInventoryFormDto,
  ['supplierId', 'supplierName']
) {}

export class MaterialReturnInventoryFormResDto extends BaseMaterialReturnInventoryFormDto {}

export class MaterialReturnInventoryFormUpdateDto extends PickType(
  BaseMaterialReturnInventoryFormDto,
  ['supplierId', 'supplierName', 'year', 'month', 'day']
) {
  @ApiProperty({
    description: '退库单编码'
  })
  code: string;
}

export class MaterialReturnInventoryFormUpdateSubmitStatusDto extends PickType(
  BaseMaterialReturnInventoryFormDto,
  ['submitStatus']
) {}

export class MaterialReturnInventoryFormUpdateAuditStatusDto extends PickType(
  BaseMaterialReturnInventoryFormDto,
  ['auditStatus']
) {}

export class MaterialReturnInventoryFormCreateDto extends PickType(
  BaseMaterialReturnInventoryFormDto,
  ['returnInventoryType']
) {}

// 明细
export class BaseMaterialReturnInventoryFormDetailDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '父级id' })
  parentId?: string;

  @ApiProperty({ description: '退库单ID' })
  @IsNotEmpty({ message: '退库单ID不能为空' })
  @IsString({ message: '退库单ID必须是字符串' })
  materialReversalId: string;

  @ApiProperty({ description: '领料单明细ID' })
  @IsOptional({ message: '领料单明细ID能为空' })
  @IsString({ message: '领料单明细ID必须是字符串' })
  materialRequisitionDetailId?: string;

  @ApiProperty({ description: '领料使用部位名称' })
  @IsOptional({ message: '领料使用部位名称能为空' })
  @IsString({ message: '领料使用部位名称必须是字符串' })
  partName?: string;

  @ApiProperty({ description: '成本科目明细ID' })
  @IsOptional({ message: '成本科目明细ID能为空' })
  @IsString({ message: '成本科目明细ID必须是字符串' })
  businessCostSubjectDetailId?: string;

  @ApiProperty({ description: '成本科目明细ID名称' })
  businessCostSubjectDetailName?: string;

  @ApiProperty({ description: '材料ID' })
  @IsNotEmpty({ message: '材料ID' })
  @IsString({ message: '材料ID必须是字符串' })
  materialId: string;

  @ApiProperty({ description: '材料名称/明细的单据编码' })
  @IsOptional({ message: '材料名称/明细的单据编码能为空' })
  @IsString({ message: '材料名称/明细的单据编码必须是字符串' })
  materialName?: string;

  @ApiProperty({ description: '材料规格/明细的单据时间' })
  @IsNotEmpty({ message: '材料规格/明细的单据时间不能为空' })
  @IsString({ message: '材料规格/明细的单据时间必须是字符串' })
  materialSpec?: string;

  @ApiProperty({ description: '计量单位' })
  @IsOptional({ message: '计量单位可以为空' })
  @IsString({ message: '计量单位必须是字符串' })
  unit?: string | null;

  @ApiProperty({ description: '领料单价' })
  @IsOptional({ message: '领料单价能为空' })
  @IsNumber({}, { message: '领料单价必须是数字' })
  requisitionPrice?: number;

  @ApiProperty({ description: '可退数量' })
  @IsNotEmpty({ message: '可退数量可以为空' })
  @IsNumber({}, { message: '可退数量必须是数字' })
  canReversalQuantity: number;

  @ApiProperty({ description: '退库数量' })
  @IsNotEmpty({ message: '退库数量不可以为空' })
  @IsNumber({}, { message: '退库数量必须是数字' })
  reversalQuantity: number;

  @ApiProperty({ description: '退库单价' })
  @IsOptional({ message: '退库单价可以为空' })
  @IsNumber({}, { message: '退库单价必须是数字' })
  reversalPrice?: number;

  @ApiProperty({ description: '退库金额' })
  @IsOptional({ message: '退库金额可以为空' })
  @IsNumber({}, { message: '退库金额必须是数字' })
  reversalAmount?: number;

  @ApiProperty({ description: '折旧摊销金额' })
  @IsOptional({ message: '折旧摊销金额可以为空' })
  @IsNumber({}, { message: '折旧摊销金额必须是数字' })
  depreciationAmount?: number;

  @ApiProperty({ description: '当前退库单的库存数量' })
  @IsOptional({ message: '当前退库单的库存数量可以为空' })
  @IsNumber({}, { message: '当前退库单的库存数量必须是数字' })
  reversalInventoryQuantity?: number;

  @ApiProperty({ description: '排序号' })
  orderNo: number;

  @ApiProperty({ description: '备注' })
  @IsOptional({ message: '备注可以为空' })
  @IsString({ message: '备注必须是字符串' })
  remark?: string | null;
}

export class MaterialReturnInventoryFormDetailResDto extends PickType(
  BaseMaterialReturnInventoryFormDetailDto,
  [
    'id',
    'parentId',
    'materialReversalId',
    'materialRequisitionDetailId',
    'materialId',
    'materialName',
    'materialSpec',
    'unit',
    'partName',
    'businessCostSubjectDetailId',
    'businessCostSubjectDetailName',
    'orderNo',
    'requisitionPrice',
    'canReversalQuantity',
    'reversalQuantity',
    'reversalPrice',
    'reversalAmount',
    'depreciationAmount',
    'reversalInventoryQuantity',
    'remark'
  ] as const
) {}

export class MaterialReturnInventoryFormDetailCreateDto {
  @ApiProperty({
    description: '数据数组'
  })
  @IsNotEmpty({ message: '数据数组不能为空' })
  @IsArray({ message: '数据数组必须为数组' })
  list: MaterialReturnInventoryFormDetailCreateListDto[];

  @ApiProperty({
    description: '退库单id'
  })
  @IsNotEmpty({ message: '退库单id不能为空' })
  @IsString({ message: '退库单id格式不正确' })
  materialReversalId: string;
}

export class MaterialReturnInventoryFormDetailCreateListDto extends PickType(
  BaseMaterialReturnInventoryFormDetailDto,
  [
    'materialId',
    'materialName',
    'materialSpec',
    'unit',
    'canReversalQuantity',
    'reversalQuantity',
    'remark',
    'orderNo'
  ] as const
) {}

export class MaterialReturnInventoryFormDetailUpdateDto extends PickType(
  BaseMaterialReturnInventoryFormDetailDto,
  ['reversalQuantity', 'depreciationAmount', 'remark'] as const
) {}

export class MaterialReturnInventoryFormChooseCategoryTreeResDto {
  @ApiProperty({
    description: 'id'
  })
  id: string;

  @ApiProperty({
    description: '编码'
  })
  code: string;

  @ApiProperty({
    description: '类别名称'
  })
  name: string;

  @ApiProperty({
    description: '核算类型'
  })
  materialType: MaterialType;

  @ApiProperty({
    description: '备注'
  })
  remark: string;
}

export class MaterialReturnInventoryFormChooseDetailsResDto {
  @ApiProperty({
    description: 'id'
  })
  id: string;

  @ApiProperty({
    description: '材料id'
  })
  materialId: string;

  @ApiProperty({
    description: '材料名称'
  })
  materialName: string;

  @ApiProperty({
    description: '材料规格型号'
  })
  materialSpec: string;

  @ApiProperty({
    description: '材料计量单位'
  })
  unit: string;

  @ApiProperty({
    description: '领料数量'
  })
  canReversalQuantity: number;
}

// export class MaterialReturnInventoryFormChooseDetailsDto extends MaterialReturnInventoryFormChooseDetailsResDto {
//   @ApiProperty({
//     description: '收料单id'
//   })
//   receivingId?: string;
// }

export class MaterialReturnInventoryFormChooseCategoryQueryDto {
  @ApiProperty({
    description: '退库单id'
  })
  @IsNotEmpty({ message: '退库单id不能为空' })
  @IsString({ message: '退库单id格式不正确' })
  materialReversalId: string;
}

export class MaterialReturnInventoryFormChooseDetailsQueryDto {
  @ApiProperty({
    description: '退库单id'
  })
  @IsNotEmpty({ message: '退库单id不能为空' })
  @IsString({ message: '退库单id格式不正确' })
  materialReversalId: string;

  @ApiProperty({
    description: '分类id'
  })
  @IsOptional()
  @IsString({ message: '分类id格式不正确' })
  categoryId?: string;
}

// 附件
export class BaseMaterialReturnInventoryFormAttachmentDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '退库单id' })
  @IsNotEmpty({ message: '退库单id不能为空' })
  @IsString({ message: '退库单id必须是字符串' })
  materialReversalId: string;

  @ApiProperty({ description: '文件名称' })
  @IsNotEmpty({ message: '文件名称不能为空' })
  @IsString({ message: '文件名称必须是字符串' })
  fileName: string;

  @ApiProperty({ description: '文件扩展名' })
  @IsNotEmpty({ message: '文件扩展名不能为空' })
  @IsString({ message: '文件扩展名必须是字符串' })
  fileExt: string;

  @ApiProperty({ description: '文件key' })
  @IsNotEmpty({ message: '文件key不能为空' })
  @IsString({ message: '文件key必须是字符串' })
  fileKey: string;

  @ApiProperty({ description: '文件大小' })
  @IsNotEmpty({ message: '文件大小不能为空' })
  @IsNumber({}, { message: '文件大小必须是数字' })
  fileSize: number;

  @ApiProperty({ description: '文件类型' })
  @IsNotEmpty({ message: '文件类型不能为空' })
  @IsString({ message: '文件类型必须是字符串' })
  fileContentType: string;
}

export class MaterialReturnInventoryAttachmentResDto extends PickType(
  BaseMaterialReturnInventoryFormAttachmentDto,
  [
    'id',
    'fileName',
    'fileSize',
    'fileExt',
    'fileContentType',
    'fileKey'
  ] as const
) {}

export class MaterialReturnInventoryAttachmentCreateDto extends PickType(
  BaseMaterialReturnInventoryFormAttachmentDto,
  [
    'materialReversalId',
    'fileName',
    'fileSize',
    'fileExt',
    'fileContentType',
    'fileKey'
  ] as const
) {}
