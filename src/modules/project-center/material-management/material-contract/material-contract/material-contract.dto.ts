import { ApiProperty, PickType } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsBoolean,
  IsIn,
  IsNotEmpty,
  IsOptional,
  IsString
} from 'class-validator';

import {
  AuditStatus,
  ContractTemplateClassifyType,
  FulfillmentStatus,
  PartyBType,
  ProposedStatus,
  SubmitStatus
} from '@/prisma/generated';

class BaseMaterialContractDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '父级id' })
  @IsOptional({ message: '父级id可以为空' })
  @IsString({ message: '父级id必须是字符串' })
  parentId?: string | null;

  @ApiProperty({ description: '组织id（项目id）' })
  orgId: string;

  @ApiProperty({ description: '租户id' })
  tenantId: string;

  @ApiProperty({ description: '合同范本id' })
  @IsNotEmpty({ message: '合同范本id不能为空' })
  @IsString({ message: '合同范本id必须是字符串' })
  contractTemplateId: string;

  @ApiProperty({ description: '合同范本名称' })
  contractTemplateName?: string | null;

  @ApiProperty({ description: '合同类型' })
  contractTemplateType: ContractTemplateClassifyType;

  @ApiProperty({ description: '合同名称' })
  @IsNotEmpty({ message: '合同名称不能为空' })
  @IsString({ message: '合同名称必须是字符串' })
  name: string;

  @ApiProperty({ description: '合同编号' })
  @IsNotEmpty({ message: '合同编号不能为空' })
  @IsString({ message: '合同编号必须是字符串' })
  code: string;

  @ApiProperty({ description: '甲方id' })
  @IsNotEmpty({ message: '甲方不能为空' })
  @IsString({ message: '甲方必须是字符串' })
  partyA: string;

  @ApiProperty({ description: '乙方id' })
  @IsNotEmpty({ message: '乙方不能为空' })
  @IsString({ message: '乙方必须是字符串' })
  partyB: string;

  @ApiProperty({ description: '甲方名称' })
  @IsOptional()
  @IsString({ message: '甲方名称必须是字符串' })
  partyAName: string;

  @ApiProperty({ description: '乙方名称' })
  @IsOptional()
  @IsString({ message: '乙方名称必须是字符串' })
  partyBName: string;

  @ApiProperty({ description: '乙方类型' })
  @IsNotEmpty({ message: '乙方类型不能为空' })
  @IsIn(Object.values(PartyBType), {
    message: '乙方类型不在枚举范围内'
  })
  @IsString({ message: '乙方类型必须是字符串' })
  partyBType: PartyBType;

  @ApiProperty({ description: '价格类型' })
  priceType: string;

  @ApiProperty({ description: '暂定合同金额' })
  amount: string;

  @ApiProperty({ description: '编制人' })
  creator: string;

  @ApiProperty({ description: '编制时间' })
  createAt: Date;

  @ApiProperty({ description: '拟定状态' })
  @IsNotEmpty({ message: '拟定状态不能为空' })
  @IsIn(Object.values(ProposedStatus), {
    message: '拟定状态不在枚举范围内'
  })
  @IsString({ message: '拟定状态必须是字符串' })
  proposedStatus: ProposedStatus;

  @ApiProperty({ description: '履约状态' })
  @IsNotEmpty({ message: '履约状态不能为空' })
  @IsIn(Object.values(FulfillmentStatus), {
    message: '履约状态不在枚举范围内'
  })
  @IsString({ message: '履约状态必须是字符串' })
  fulfillmentStatus: FulfillmentStatus;

  @ApiProperty({ description: '提交状态' })
  @IsNotEmpty({ message: '提交状态不能为空' })
  @IsIn(Object.values(SubmitStatus), {
    message: '提交状态不在枚举范围内'
  })
  @IsString({ message: '提交状态必须是字符串' })
  submitStatus: SubmitStatus;

  @ApiProperty({ description: '审核状态状态' })
  @IsNotEmpty({ message: '审核状态状态不能为空' })
  @IsIn(Object.values(AuditStatus), {
    message: '审核状态不在枚举范围内'
  })
  @IsString({ message: '审核状态必须是字符串' })
  auditStatus: AuditStatus;

  @ApiProperty({ description: '合同说明' })
  @IsOptional({ message: '合同说明可以为空' })
  @IsString({ message: '合同说明必须是字符串' })
  remark?: string | null;

  @ApiProperty({ description: '文件名称' })
  @IsNotEmpty({ message: '文件名称不能为空' })
  @IsString({ message: '文件名称必须是字符串' })
  fileName: string;

  @ApiProperty({ description: '文件扩展名' })
  @IsNotEmpty({ message: '文件扩展名不能为空' })
  @IsString({ message: '文件扩展名必须是字符串' })
  fileExt: string;

  @ApiProperty({ description: '文件key' })
  @IsNotEmpty({ message: '文件key不能为空' })
  @IsString({ message: '文件key必须是字符串' })
  fileKey: string;

  @ApiProperty({ description: '文件大小' })
  @IsNotEmpty({ message: '文件大小不能为空' })
  @IsString({ message: '文件大小必须是字符串' })
  fileSize: string;

  @ApiProperty({ description: '文件类型' })
  @IsNotEmpty({ message: '文件类型不能为空' })
  @IsString({ message: '文件类型必须是字符串' })
  fileContentType: string;
}

export class MaterialContractResDto extends PickType(BaseMaterialContractDto, [
  'id',
  'parentId',
  'orgId',
  'tenantId',
  'contractTemplateId',
  'contractTemplateName',
  'contractTemplateType',
  'name',
  'code',
  'partyA',
  'partyAName',
  'partyB',
  'partyBName',
  'partyBType',
  'creator',
  'proposedStatus',
  'submitStatus',
  'fulfillmentStatus',
  'auditStatus',
  'createAt',
  'priceType',
  'amount',
  'remark',
  'fileExt',
  'fileKey',
  'fileName',
  'fileSize',
  'fileContentType'
] as const) {}

export class QueryMaterialContractDto {
  @ApiProperty({ description: '提交状态' })
  @IsOptional()
  @IsIn(Object.values(SubmitStatus), {
    message: '提交状态不在枚举范围内'
  })
  @IsString({ message: '提交状态必须是字符串' })
  submitStatus?: SubmitStatus;

  @ApiProperty({ description: '审核状态状态' })
  @IsOptional()
  @IsIn(Object.values(AuditStatus), {
    message: '审核状态不在枚举范围内'
  })
  @IsString({ message: '审核状态必须是字符串' })
  auditStatus?: AuditStatus;

  @ApiProperty({ description: '是否查询子级节点' })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') {
      return true;
    }
    if (value === 'false') {
      return false;
    }
    return value;
  })
  @IsBoolean({ message: '是否查询子级节点必须是布尔值' })
  isQueryChild?: boolean;
}

export class MaterialContractCreateDto extends PickType(
  BaseMaterialContractDto,
  [
    'parentId',
    'contractTemplateId',
    'name',
    'code',
    'partyA',
    'partyB',
    'partyAName',
    'partyBName',
    'partyBType',
    'proposedStatus',
    'remark'
  ] as const
) {}

export class MaterialContractUpdateDto extends PickType(
  BaseMaterialContractDto,
  ['name', 'code', 'proposedStatus', 'remark'] as const
) {}

export class MaterialContractUpdateSubmitStatusDto extends PickType(
  BaseMaterialContractDto,
  ['submitStatus'] as const
) {}

export class MaterialContractUpdateAuditStatusDto extends PickType(
  BaseMaterialContractDto,
  ['auditStatus'] as const
) {}

export class CheckRequiredFieldDto extends PickType(BaseMaterialContractDto, [
  'contractTemplateId',
  'id'
] as const) {}
