import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import {
  AuditStatus,
  ContractTemplateClassifyType,
  PartyBType,
  Prisma,
  ProposedStatus,
  SettlementBillType,
  SubmitStatus
} from '@/prisma/generated';

import {
  MaterialReceivingReturnDetailQueryDto,
  MaterialSettlementDetailQueryDto,
  QueryReceivingReturnDto,
  SettlementSupplierAndContractResponseDto,
  SumReceivingReturnBodyDto,
  TransferInProjectResponseDto
} from './material-settlement-detail.dto';

@Injectable()
export class MaterialSettlementDetailRepository {
  constructor(private readonly prisma: PrismaService) {}

  async selectTransferInProjectList(
    reqUser: IReqUser
  ): Promise<TransferInProjectResponseDto[]> {
    const result = await this.prisma.$queryRaw<any[]>`
    select 
      distinct o.id as project_id,
      o.seal_name as project_name
    from material_allocation_from a
    join platform_meta."org" o
      on o.id = a.transfer_in_project_id
      and o.tenant_id = a.tenant_id
    where a.tenant_id = ${reqUser.tenantId}
        and a.org_id = ${reqUser.orgId}
        and a.is_deleted = false
    `;

    return result;
  }

  // 获取物资结算单统计周期
  async selectSupplierAndContractList(
    reqUser: IReqUser
  ): Promise<SettlementSupplierAndContractResponseDto[]> {
    const result = await this.prisma.$queryRaw<any[]>`
      -- 正式合同 乙方是公司 获取物资采购类型下已审核的合同（不含补充协议）
      select 
        a.id as contract_id,
        a.name as contract_name,
        TO_CHAR(a.sign_date, 'YYYY-MM-DD') as sign_date,
        a.party_b as supplier_id,
        a.party_b_end_name as supplier_name
      from material_contract a
      join contract_template b
        on b.is_deleted = false
        and b.tenant_id = a.tenant_id
        and b.id = a.contract_template_id
        and b.classify = ${ContractTemplateClassifyType.MATERIALS_PURCHASING}::"ContractTemplateClassifyType"
      where a.tenant_id = ${reqUser.tenantId}
        and a.org_id = ${reqUser.orgId}
        and a.is_deleted = false
        and a.parent_id is null
        and a.party_b_type = ${PartyBType.COMPANY}::"PartyBType"
        and a.proposed_status = ${ProposedStatus.OFFICIAL}::"ProposedStatus"
        and a.audit_status = ${AuditStatus.APPROVED}::"AuditStatus"
      union all
      -- 正式合同 乙方是供应商 获取物资采购类型下已审核的合同（不含补充协议）
      select 
        a.id as contract_id,
        a.name as contract_name,
        TO_CHAR(a.sign_date, 'YYYY-MM-DD') as sign_date,
        a.party_b as supplier_id,
        a.party_b_end_name as supplier_name
      from material_contract a
      join contract_template b
        on b.is_deleted = false
        and b.tenant_id = a.tenant_id
        and b.id = a.contract_template_id
        and b.classify = ${ContractTemplateClassifyType.MATERIALS_PURCHASING}::"ContractTemplateClassifyType"
      where a.tenant_id = ${reqUser.tenantId}
        and a.org_id = ${reqUser.orgId}
        and a.is_deleted = false
        and a.parent_id is null
        and a.party_b_type = ${PartyBType.SUPPLIER}::"PartyBType"
        and a.proposed_status = ${ProposedStatus.OFFICIAL}::"ProposedStatus"
        and a.audit_status = ${AuditStatus.APPROVED}::"AuditStatus"
      
      union all
      -- 暂定合同 乙方是公司 获取物资采购类型下已提交的合同（不含补充协议）
      select 
        a.id as contract_id,
        a.name as contract_name,
        TO_CHAR(a.sign_date, 'YYYY-MM-DD') as sign_date,
        a.party_b as supplier_id,
        a.party_b_end_name as supplier_name
      from material_contract a
      join contract_template b
        on b.is_deleted = false
        and b.tenant_id = a.tenant_id
        and b.id = a.contract_template_id
        and b.classify = ${ContractTemplateClassifyType.MATERIALS_PURCHASING}::"ContractTemplateClassifyType"
      where a.tenant_id = ${reqUser.tenantId}
        and a.org_id = ${reqUser.orgId}
        and a.is_deleted = false
        and a.parent_id is null
        and a.party_b_type = ${PartyBType.COMPANY}::"PartyBType"
        and a.proposed_status = ${ProposedStatus.PROVISIONAL}::"ProposedStatus"
        and a.submit_status = ${SubmitStatus.SUBMITTED}::"SubmitStatus"
      union all
      -- 暂定合同 乙方是供应商 获取物资采购类型下已提交的合同（不含补充协议）
      select 
        a.id as contract_id,
        a.name as contract_name,
        TO_CHAR(a.sign_date, 'YYYY-MM-DD') as sign_date,
        a.party_b as supplier_id,
        a.party_b_end_name as supplier_name
      from material_contract a
      join contract_template b
        on b.is_deleted = false
        and b.tenant_id = a.tenant_id
        and b.id = a.contract_template_id
        and b.classify = ${ContractTemplateClassifyType.MATERIALS_PURCHASING}::"ContractTemplateClassifyType"
      where a.tenant_id = ${reqUser.tenantId}
        and a.org_id = ${reqUser.orgId}
        and a.is_deleted = false
        and a.parent_id is null
        and a.party_b_type = ${PartyBType.SUPPLIER}::"PartyBType"
        and a.proposed_status = ${ProposedStatus.PROVISIONAL}::"ProposedStatus"
        and a.submit_status = ${SubmitStatus.SUBMITTED}::"SubmitStatus"
    `;

    return result;
  }

  async selectAllocationFromList(
    reqUser: IReqUser,
    query: TransferInProjectResponseDto
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
      with bill as (
        select 
          distinct bill_id
        from material_settlement_bill_ref_detail
        where tenant_id = ${reqUser.tenantId}
          and org_id = ${reqUser.orgId}
          and is_deleted = false
          and settlement_id != ${query.settlementId}
      ),
      cur_bill as (
        select 
          distinct bill_id
        from material_settlement_bill_ref_detail
        where tenant_id = ${reqUser.tenantId}
          and org_id = ${reqUser.orgId}
          and is_deleted = false
          and settlement_id = ${query.settlementId}
      ),
      temp_material_categories as (
        select
          mrd.material_allocation_from_id,
          STRING_AGG(distinct
            case
              when position('|' in mdc.full_name) > 0
              then split_part(mdc.full_name, '|', 2)
              else split_part(mdc.full_name, '|', 1)
            end,
            ','
          ) filter (where mdc.full_name is not null) material_categories
        from material_allocation_from_detail mrd
        join material_dictionary_detail mdd
          on mdd.id = mrd.material_id
          and mdd.is_deleted = false
          and mdd.tenant_id = ${reqUser.tenantId}
        join material_dictionary_category mdc
          on mdc.id = mdd.material_dictionary_category_id
          and mdc.is_deleted = false
          and mdc.tenant_id = ${reqUser.tenantId}
          and mdc.org_id = mdd.org_id
        where mrd.is_deleted = false
          and mrd.tenant_id = ${reqUser.tenantId}
          and mrd.org_id = ${reqUser.orgId}
          and mrd.parent_id is null
          and mrd.material_allocation_from_id not in (select bill_id from bill)
        group by mrd.material_allocation_from_id
      )
      select
        mr.id
        ,mr.code
        ,tmc.material_categories
        ,mr.amount as tax_excluded_amount
        ,u.nickname as creator
        ,mr.submit_status
        ,mr.audit_status
        ,TO_CHAR(MAKE_DATE(mr.year, mr.month, mr.day), 'YYYY-MM-DD') AS bill_date
        ,'ALLOCATION_FROM' as settlement_bill_type
        , (cur_bill.bill_id IS NOT NULL)::boolean as is_selected
      from material_allocation_from mr
      left join temp_material_categories tmc
        on tmc.material_allocation_from_id = mr.id
      left join cur_bill
        on cur_bill.bill_id = mr.id
      left join platform_meta."user" u
        on u.id = mr.create_by
      where mr.tenant_id = ${reqUser.tenantId}
        and mr.org_id = ${reqUser.orgId}
        and mr.is_deleted = false
        and mr.transfer_in_project_id = ${query.projectId}
        and mr.id not in (select bill_id from bill)
    `;

    return result;
  }

  async selectReceivingReturnList(
    reqUser: IReqUser,
    query: QueryReceivingReturnDto
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
      with bill as (
        select 
          distinct bill_id
        from material_settlement_bill_ref_detail
        where tenant_id = ${reqUser.tenantId}
          and org_id = ${reqUser.orgId}
          and is_deleted = false
          and settlement_id != ${query.settlementId}
      ),
      cur_bill as (
        select 
          distinct bill_id
        from material_settlement_bill_ref_detail
        where tenant_id = ${reqUser.tenantId}
          and org_id = ${reqUser.orgId}
          and is_deleted = false
          and settlement_id = ${query.settlementId}
      ),
      temp_material_categories as (
        select
          mrd.receiving_id,
          STRING_AGG(distinct
            case
              when position('|' in mdc.full_name) > 0
              then split_part(mdc.full_name, '|', 2)
              else split_part(mdc.full_name, '|', 1)
            end,
            ','
          ) filter (where mdc.full_name is not null) material_categories
        from material_receiving_detail mrd
        join material_dictionary_detail mdd
          on mdd.id = mrd.material_id
          and mdd.is_deleted = false
          and mdd.tenant_id = ${reqUser.tenantId}
        join material_dictionary_category mdc
          on mdc.id = mdd.material_dictionary_category_id
          and mdc.is_deleted = false
          and mdc.tenant_id = ${reqUser.tenantId}
          and mdc.org_id = mdd.org_id
        where mrd.is_deleted = false
          and mrd.tenant_id = ${reqUser.tenantId}
          and mrd.org_id = ${reqUser.orgId}
          and mrd.receiving_id not in (select bill_id from bill)
        group by mrd.receiving_id
      ),
      receiving_bill as (
        select
          mr.id
          ,mr.code
          ,mr.purchase_type
          ,tmc.material_categories
          ,mr.tax_excluded_amount
          ,u.nickname as creator
          ,mr.submit_status
          ,mr.audit_status
          ,TO_CHAR(MAKE_DATE(mr.year, mr.month, mr.day), 'YYYY-MM-DD') AS bill_date
          ,'RECEIVING' as settlement_bill_type
          , (cur_bill.bill_id IS NOT NULL)::boolean as is_selected
        from material_receiving mr
        left join temp_material_categories tmc
          on tmc.receiving_id = mr.id
        left join cur_bill
          on cur_bill.bill_id = mr.id
        left join platform_meta."user" u
          on u.id = mr.create_by
        where mr.tenant_id = ${reqUser.tenantId}
          and mr.org_id = ${reqUser.orgId}
          and mr.is_deleted = false
          and mr.contract_id = ${query.contractId}
          and mr.supplier_id = ${query.supplierId}
          and mr.id not in (select bill_id from bill)
      ),
      temp_material_categories2 as (
        select
          mrsfd.return_sales_form_id,
          STRING_AGG(distinct
            case
              when position('|' in mdc.full_name) > 0
              then split_part(mdc.full_name, '|', 2)
              else split_part(mdc.full_name, '|', 1)
            end,
            ','
          ) filter (where mdc.full_name is not null) material_categories
        from material_return_sales_form_detail mrsfd
        join material_dictionary_detail mdd
          on mdd.id = mrsfd.material_id
          and mdd.is_deleted = false
          and mdd.tenant_id = ${reqUser.tenantId}
        join material_dictionary_category mdc
          on mdc.id = mdd.material_dictionary_category_id
          and mdc.is_deleted = false
          and mdc.tenant_id = ${reqUser.tenantId}
          and mdc.org_id = mdd.org_id
        where mrsfd.is_deleted = false
          and mrsfd.tenant_id = ${reqUser.tenantId}
          and mrsfd.org_id = ${reqUser.orgId}
          and mrsfd.return_sales_form_id not in (select bill_id from bill)
        group by mrsfd.return_sales_form_id
      ),
      return_bill as (
        select
          mrsf.id
          ,mrsf.code
          ,mrsf.purchase_type
          ,tmc2.material_categories
			  	,mrsf.amount as tax_excluded_amount
          ,u.nickname as creator
          ,mrsf.submit_status
          ,mrsf.audit_status
          ,TO_CHAR(MAKE_DATE(mrsf.year, mrsf.month, mrsf.day), 'YYYY-MM-DD') AS bill_date
          ,'RETURN_SALES' as settlement_bill_type
          ,(cur_bill.bill_id IS NOT NULL)::boolean as is_selected
        from material_return_sales_form mrsf
        left join temp_material_categories2 tmc2
          on tmc2.return_sales_form_id = mrsf.id
        left join cur_bill
          on cur_bill.bill_id = mrsf.id
        left join platform_meta."user" u
          on u.id = mrsf.create_by
        where mrsf.tenant_id = ${reqUser.tenantId}
          and mrsf.org_id = ${reqUser.orgId}
          and mrsf.is_deleted = false
          and mrsf.contract_id = ${query.contractId}
          and mrsf.supplier_id = ${query.supplierId}
          and mrsf.id not in (select bill_id from bill)
      ),
      bills as (
        select 
          * 
        from receiving_bill
        union all
        select 
          * 
        from return_bill
      )
      select 
        bills.*
      from bills
      order by submit_status desc, code desc, bill_date desc
    `;

    return result;
  }

  async selectReceivingReturnItems(
    reqUser: IReqUser,
    body: SumReceivingReturnBodyDto
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
    select 
      id
      , material_id
      , material_name
      , material_spec
      , unit
      , actual_quantity as quantity
      , price_excluding_tax
      , tax_excluded_amount
      , remark
      , receiving_id as bill_id
      , 'RECEIVING' as settlement_bill_type
    from material_receiving_detail
    where is_deleted = false
      and tenant_id = ${reqUser.tenantId}
      and org_id = ${reqUser.orgId}
      and receiving_id in (${Prisma.join(body.receivingReturnIds)})

    union all
    select 
      id
      , material_id
      , material_name
      , material_spec
      , unit
      , COALESCE(return_sales_quantity, 0) * -1  as quantity
      , return_sales_price as price_excluding_tax
      , COALESCE(return_sales_amount, 0) * -1 as tax_excluded_amount
      , remark
      , return_sales_form_id as bill_id
      , 'RETURN_SALES' as settlement_bill_type
    from material_return_sales_form_detail
    where is_deleted = false
      and tenant_id = ${reqUser.tenantId}
      and org_id = ${reqUser.orgId}
      and return_sales_form_id in (${Prisma.join(body.receivingReturnIds)})
      and parent_id is null

    union all
    select 
      id
      , material_id
      , material_name
      , material_spec
      , unit
      , allocation_quantity  as quantity
      , allocation_price as price_excluding_tax
      , allocation_amount as tax_excluded_amount
      , remark
      , material_allocation_from_id as bill_id
      , 'ALLOCATION_FROM' as settlement_bill_type
    from material_allocation_from_detail
    where is_deleted = false
      and tenant_id = ${reqUser.tenantId}
      and org_id = ${reqUser.orgId}
      and material_allocation_from_id in (${Prisma.join(body.receivingReturnIds)})
      and parent_id is null
    `;

    return result;
  }

  async updateBusinessCostSubjectDetailId(
    tx: PrismaService,
    reqUser: IReqUser,
    body: SumReceivingReturnBodyDto
  ) {
    await tx.$executeRaw<any[]>`
    with businessCostSubject as (
      select 
        material_dictionary_detail_id as material_id
        , string_agg(distinct business_cost_subject_detail_id, ',') as business_cost_subject_detail_id
      from account_material_detail_business_cost_subject_detail
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and org_id = ${reqUser.orgId}
        and COALESCE(business_cost_subject_detail_id, '') != ''
      group by material_dictionary_detail_id
        having count(id) = 1
    )
    update material_settlement_detail a
    set business_cost_subject_detail_id = businessCostSubject.business_cost_subject_detail_id
    from businessCostSubject
    where a.is_deleted = false
      and a.tenant_id = ${reqUser.tenantId}
      and a.org_id = ${reqUser.orgId}
      and a.settlement_id = ${body.settlementId}
      and a.material_id = businessCostSubject.material_id
      and a.business_cost_subject_detail_id is null
      and a.is_manual = false
    `;

    return true;
  }

  async updateMaterialSettlementAmount(
    tx: PrismaService,
    reqUser: IReqUser,
    body: SumReceivingReturnBodyDto
  ) {
    await tx.$executeRaw<any[]>`
    with detail as (
      select
        settlement_id,
        settlement_amount
      from material_settlement_detail
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and org_id = ${reqUser.orgId}
        and settlement_id = ${body.settlementId}
      union all
      select 
        ${body.settlementId} as settlement_id,
        0 as settlement_amount
    ),
    total as (
      select
        settlement_id,
        sum(settlement_amount) as tax_excluded_amount
      from detail
      group by settlement_id
    )
    update material_settlement ms 
    set tax_excluded_amount = total.tax_excluded_amount,
      tax_included_amount = total.tax_excluded_amount * (1 + COALESCE(ms.tax_rate, 0)),
      tax_amount = total.tax_excluded_amount * COALESCE(ms.tax_rate, 0),
      update_by = ${reqUser.id},
      update_at = NOW()
    from total
    where ms.is_deleted = false
      and ms.tenant_id = ${reqUser.tenantId}
      and ms.org_id = ${reqUser.orgId}
      and ms.id = ${body.settlementId}
      and total.settlement_id = ms.id
    `;

    return true;
  }

  async updateMaterialSettlementDetail(
    tx: PrismaService,
    reqUser: IReqUser,
    body: SumReceivingReturnBodyDto
  ) {
    await tx.$executeRaw<any[]>`
      with remove_bill_detail as (
        select
          a.settlement_id
          , a.settlement_detail_id
          , b.actual_quantity as quantity
          , b.tax_excluded_amount
        from material_settlement_bill_ref_detail a
        join material_receiving_detail b
          on a.tenant_id = b.tenant_id
          and a.org_id = b.org_id
          and b.is_deleted = false
          and a.bill_id = b.receiving_id
          and a.bill_detail_id = b.id
        where a.is_deleted = false
          and a.tenant_id = ${reqUser.tenantId}
          and a.org_id = ${reqUser.orgId}
          and a.settlement_id = ${body.settlementId}
          and a.bill_id in (${Prisma.join(body.receivingReturnIds)})

        union all
        select
          a.settlement_id
          , a.settlement_detail_id
          , b.return_sales_quantity * -1 as quantity
          , b.return_sales_amount * -1 as tax_excluded_amount
        from material_settlement_bill_ref_detail a
        join material_return_sales_form_detail b
          on a.tenant_id = b.tenant_id
          and a.org_id = b.org_id
          and b.is_deleted = false
          and a.bill_id = b.return_sales_form_id
          and a.bill_detail_id = b.id
        where a.is_deleted = false
          and a.tenant_id = ${reqUser.tenantId}
          and a.org_id = ${reqUser.orgId}
          and a.settlement_id = ${body.settlementId}
          and a.bill_id in (${Prisma.join(body.receivingReturnIds)})
        
        union all
        select
          a.settlement_id
          , a.settlement_detail_id
          , b.allocation_quantity as quantity
          , b.allocation_amount as tax_excluded_amount
        from material_settlement_bill_ref_detail a
        join material_allocation_from_detail b
          on a.tenant_id = b.tenant_id
          and a.org_id = b.org_id
          and b.is_deleted = false
          and a.bill_id = b.material_allocation_from_id
          and a.bill_detail_id = b.id
          and b.parent_id is null
        where a.is_deleted = false
          and a.tenant_id = ${reqUser.tenantId}
          and a.org_id = ${reqUser.orgId}
          and a.settlement_id = ${body.settlementId}
          and a.bill_id in (${Prisma.join(body.receivingReturnIds)})
      ),
      remove_total as (
        select
          settlement_id
          , settlement_detail_id
          , sum(quantity) as quantity
          , sum(tax_excluded_amount) as tax_excluded_amount
        from remove_bill_detail
        group by settlement_id, settlement_detail_id
      )
      update material_settlement_detail a
      set amount = a.amount - b.tax_excluded_amount,
        quantity = a.quantity - b.quantity,
        settlement_amount = (a.settlement_quantity - b.quantity) * a.settlement_price,
        settlement_quantity = a.settlement_quantity - b.quantity,
        update_by = ${reqUser.id},
        update_at = NOW()
      from remove_total b
      where a.is_deleted = false
        and a.tenant_id = ${reqUser.tenantId}
        and a.org_id = ${reqUser.orgId}
        and a.settlement_id = ${body.settlementId}
        and a.id = b.settlement_detail_id
        and a.settlement_id = b.settlement_id
    `;

    return true;
  }

  async removeMaterialSettlementDetail(
    tx: PrismaService,
    reqUser: IReqUser,
    body: SumReceivingReturnBodyDto
  ) {
    await tx.$executeRaw<any[]>`
    update material_settlement_detail a
      set 
        is_deleted = true,
        update_by = ${reqUser.id},
        update_at = NOW()
      where a.is_deleted = false
        and a.tenant_id = ${reqUser.tenantId}
        and a.org_id = ${reqUser.orgId}
        and a.settlement_id = ${body.settlementId}
        and a.is_manual = false
        and a.id not in (
          select 
            distinct settlement_detail_id
          from material_settlement_bill_ref_detail
          where is_deleted = false
            and tenant_id = ${reqUser.tenantId}
            and org_id = ${reqUser.orgId}
            and settlement_id = ${body.settlementId}
        )
    `;

    return true;
  }

  async selectMaterialSettlementDetails(
    reqUser: IReqUser,
    query: MaterialSettlementDetailQueryDto
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
    select 
      a.id
      , a.settlement_id
      , a.material_id
      , a.material_name
      , a.material_spec
      , a.unit
      , a.quantity
      , a.price
      , a.amount
      , a.settlement_quantity
      , a.settlement_price
      , a.settlement_amount
      , a.order_no
      , a.is_auto_calculation
      , a.is_manual
      , a.business_cost_subject_detail_id
      , a.remark
      , b.name as business_cost_subject_detail_name
    from material_settlement_detail a
    left join business_cost_subject_detail b
      on b.is_deleted = false
      and b.tenant_id = a.tenant_id
      and b.id = a.business_cost_subject_detail_id
    where a.is_deleted = false
      and a.tenant_id = ${reqUser.tenantId}
      and a.org_id = ${reqUser.orgId}
      and a.settlement_id = ${query.settlementId}
    order by a.order_no desc
    `;

    return result;
  }

  async selectMaterialReceivingReturnDetails(
    reqUser: IReqUser,
    query: MaterialReceivingReturnDetailQueryDto
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
    with receiving_detail as (
      select
        a.settlement_bill_type
        , a.bill_id
        , a.bill_detail_id
        , a.bill_detail_id as id
        , '' as parent_id
        , c.code
        , TO_CHAR(MAKE_DATE(c.year, c.month, c.day), 'YYYY-MM-DD') AS bill_date
        , u.nickname as creator
        , b.unit
        , b.actual_quantity as quantity
        , b.price_excluding_tax as settlement_price
        , b.tax_excluded_amount
      from material_settlement_bill_ref_detail a
      join material_receiving_detail b
        on a.tenant_id = b.tenant_id
        and a.org_id = b.org_id
        and b.is_deleted = false
        and a.bill_id = b.receiving_id
        and a.bill_detail_id = b.id
      join material_receiving c
        on c.tenant_id = b.tenant_id
        and c.org_id = b.org_id
        and c.is_deleted = false
        and c.id = b.receiving_id
      left join platform_meta."user" u
        on u.id = c.create_by
      where a.is_deleted = false
        and a.tenant_id = ${reqUser.tenantId}
        and a.org_id = ${reqUser.orgId}
        and a.settlement_id = ${query.settlementId}
        and a.settlement_detail_id = ${query.settlementDetailId}
    )
    select
      *
    from receiving_detail

    union all 
    select
      ${SettlementBillType.INCOMING_INSPECTION}::"SettlementBillType" as settlement_bill_type
      , miid.incoming_inspection_id as bill_id
      , mrid.id as bill_detail_id
      , mrid.id as id
      , a.bill_detail_id as parent_id
      , mii.code
      , TO_CHAR(MAKE_DATE(mii.year, mii.month, mii.day), 'YYYY-MM-DD') AS bill_date
      , u.nickname as creator
      , miid.unit
      , miid.actual_quantity as quantity
      , null as settlement_price
      , null as tax_excluded_amount
    from receiving_detail a
    join material_receiving_incoming_detail mrid
      on mrid.receiving_detail_id = a.bill_detail_id
    join material_incoming_inspection_detail miid
      on miid.id = mrid.incoming_inspection_detail_id
      and miid.is_deleted = false
      and miid.org_id = mrid.org_id
      and miid.tenant_id = mrid.tenant_id
    join material_incoming_inspection mii
      on mii.id = miid.incoming_inspection_id
      and mii.is_deleted = false
      and mii.org_id = miid.org_id
      and mii.tenant_id = miid.tenant_id
    left join platform_meta."user" u
      on u.id = mii.create_by
    where mrid.is_deleted = false
      and mrid.org_id = ${reqUser.orgId}
      and mrid.tenant_id = ${reqUser.tenantId}

    union all
    select
      a.settlement_bill_type
      , a.bill_id
      , a.bill_detail_id
      , a.bill_detail_id as id
      , '' as parent_id
      , c.code
      , TO_CHAR(MAKE_DATE(c.year, c.month, c.day), 'YYYY-MM-DD') AS bill_date
      , u.nickname as creator
      , b.unit
      , b.return_sales_quantity * -1 as quantity
      , b.return_sales_price as settlement_price
      , b.return_sales_amount * -1 as tax_excluded_amount
    from material_settlement_bill_ref_detail a
    join material_return_sales_form_detail b
      on a.tenant_id = b.tenant_id
      and a.org_id = b.org_id
      and b.is_deleted = false
      and a.bill_id = b.return_sales_form_id
      and a.bill_detail_id = b.id
    join material_return_sales_form c
      on c.tenant_id = b.tenant_id
      and c.org_id = b.org_id
      and c.is_deleted = false
      and c.id = b.return_sales_form_id
    left join platform_meta."user" u
      on u.id = c.create_by
    where a.is_deleted = false
      and a.tenant_id = ${reqUser.tenantId}
      and a.org_id = ${reqUser.orgId}
      and a.settlement_id = ${query.settlementId}
      and a.settlement_detail_id = ${query.settlementDetailId}
    `;

    return result;
  }
}
