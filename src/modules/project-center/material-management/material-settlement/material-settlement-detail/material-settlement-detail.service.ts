import { add, multiply } from '@ewing/infra-cloud-sdk/dist/common-utils';
import { Injectable } from '@nestjs/common';
import { Decimal } from '@prisma/client/runtime/library';
import * as uuid from 'uuid';

import { CommonRepositories } from '@/common/common-repositories';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { PlatformService } from '@/modules/platform/platform.service';

import {
  MaterialReceivingReturnDetailQueryDto,
  MaterialSettlementDetailQueryDto,
  MaterialSettlementDetailResponseDto,
  QueryReceivingReturnDto,
  ReceivingReturnResponseDto,
  SettlementBillRefDto,
  SettlementSupplierAndContractResponseDto,
  SumReceivingReturnBodyDto,
  SumReceivingReturnResponseDto,
  TransferInProjectResponseDto
} from './material-settlement-detail.dto';
import { MaterialSettlementDetailRepository } from './material-settlement-detail.repository';

@Injectable()
export class MaterialSettlementDetailService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly repository: MaterialSettlementDetailRepository,
    private readonly platformService: PlatformService
  ) {}

  async getSupplierAndContractList(
    reqUser: IReqUser
  ): Promise<SettlementSupplierAndContractResponseDto[]> {
    const result = await this.repository.selectSupplierAndContractList(reqUser);
    return result;
  }

  async getTransferInProjectList(
    reqUser: IReqUser
  ): Promise<TransferInProjectResponseDto[]> {
    const result = await this.repository.selectTransferInProjectList(reqUser);
    return result;
  }

  async getAllocationFromList(
    reqUser: IReqUser,
    query: TransferInProjectResponseDto
  ): Promise<ReceivingReturnResponseDto[]> {
    const result = await this.repository.selectAllocationFromList(
      reqUser,
      query
    );
    return result;
  }

  async getReceivingReturnList(
    reqUser: IReqUser,
    query: QueryReceivingReturnDto
  ): Promise<ReceivingReturnResponseDto[]> {
    const result = await this.repository.selectReceivingReturnList(
      reqUser,
      query
    );
    return result;
  }

  async selectReceivingReturnSummaryItems(
    reqUser: IReqUser,
    body: SumReceivingReturnBodyDto
  ) {
    const result = await this.repository.selectReceivingReturnItems(
      reqUser,
      body
    );
    const retMap: Record<string, any> = {};

    result.forEach((item) => {
      const key = this.getSameMaterialKey(item);
      const sumItem = retMap[key] || {
        materialId: item.materialId,
        materialName: item.materialName,
        materialSpec: item.materialSpec,
        unit: item.unit,
        quantity: Decimal(0),
        taxExcludedAmount: Decimal(0),
        priceExcludingTax: Decimal(item.priceExcludingTax),
        remarks: [],
        refItems: []
      };

      sumItem.quantity = add(item.quantity, sumItem.quantity);
      sumItem.taxExcludedAmount = add(
        item.taxExcludedAmount,
        sumItem.taxExcludedAmount
      );
      sumItem.refItems.push({
        settlementBillType: item.settlementBillType,
        billId: item.billId,
        billDetailId: item.id
      });
      if (item.remark) sumItem.remarks.push(item.remark);
      if (!retMap[key]) {
        retMap[key] = sumItem;
      }
    });

    return retMap;
  }

  async sumReceivingReturnList(
    reqUser: IReqUser,
    body: SumReceivingReturnBodyDto
  ): Promise<SumReceivingReturnResponseDto[]> {
    const retMap = await this.selectReceivingReturnSummaryItems(reqUser, body);
    const rets = Object.values(retMap);

    rets.forEach((item) => {
      item.remarks = item.remark?.join(',') || '';
    });

    return rets.sort((a, b) => a.materialId.localeCompare(b.materialId));
  }

  getSameMaterialKey(item: SumReceivingReturnResponseDto) {
    return `${item.materialId}-${item.unit}-${item.priceExcludingTax || item.settlementPrice}`;
  }

  // 1. 查询待添加的数据
  // 2. 查询明细已存在的数据
  // 3. 待添加的数据在明细中已存在，直接使用明细id，否则使用新id
  // 4. 更新或写入明细数据，写入关系数据，更新主表金额相关数据
  async updateSettlementDetails(
    reqUser: IReqUser,
    body: SumReceivingReturnBodyDto
  ) {
    const retMap = await this.selectReceivingReturnSummaryItems(reqUser, body);
    const items = await this.getSettlementDetails(reqUser, body);
    const itemMap: Record<string, any> = {};

    items.forEach((item) => {
      const key = this.getSameMaterialKey(
        item as unknown as SumReceivingReturnResponseDto
      );
      itemMap[key] = item;
    });
    const updateItems: any = [];
    const addItems: any = [];
    const settlementBillRefItems: SettlementBillRefDto[] = [];

    const userId = reqUser.id;
    for (const key in retMap) {
      const sumItem = retMap[key];
      const detailItem = itemMap[key];
      let settlementDetailId;
      if (detailItem) {
        settlementDetailId = detailItem.id;
        updateItems.push({
          id: detailItem.id,
          quantity: add(sumItem.quantity, detailItem.quantity),
          amount: add(sumItem.taxExcludedAmount, detailItem.amount),
          settlementQuantity: add(sumItem.quantity, detailItem.quantity),
          settlementAmount: multiply(
            add(sumItem.quantity, detailItem.quantity),
            sumItem.settlementPrice
          )
        });
      } else {
        settlementDetailId = uuid.v7();
        addItems.push({
          id: settlementDetailId,
          settlementId: body.settlementId,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          createBy: userId,
          updateBy: userId,
          materialId: sumItem.materialId,
          materialName: sumItem.materialName,
          materialSpec: sumItem.materialSpec,
          unit: sumItem.unit,
          quantity: sumItem.quantity,
          price: sumItem.priceExcludingTax,
          amount: sumItem.taxExcludedAmount,
          settlementQuantity: sumItem.quantity,
          settlementPrice: sumItem.priceExcludingTax,
          settlementAmount: sumItem.taxExcludedAmount,
          isAutoCalculation: true,
          isManual: false,
          businessCostSubjectDetailId: null
        });
      }
      sumItem.refItems.forEach((item: any) => {
        settlementBillRefItems.push({
          ...item,
          settlementDetailId,
          settlementId: body.settlementId,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          createBy: userId,
          updateBy: userId
        });
      });
    }
    await this.prisma.$transaction(async (tx) => {
      // 生成关系数据
      await tx.materialSettlementBillRefDetail.createMany({
        data: settlementBillRefItems
      });
      // 更新明细数据
      for (const updateItem of updateItems) {
        await tx.materialSettlementDetail.update({
          where: {
            id: updateItem.id,
            tenantId: reqUser.tenantId,
            orgId: reqUser.orgId
          },
          data: {
            quantity: updateItem.quantity,
            amount: updateItem.amount,
            settlementQuantity: updateItem.settlementQuantity,
            settlementAmount: updateItem.settlementAmount,
            updateBy: userId
          }
        });
      }
      if (addItems.length) {
        // 写入新明细数据
        await tx.materialSettlementDetail.createMany({
          data: addItems
        });
      }

      // 更新明细对应的业务成本科目关系数据
      await this.repository.updateBusinessCostSubjectDetailId(
        tx as PrismaService,
        reqUser,
        body
      );

      // 更新主表数据
      await this.repository.updateMaterialSettlementAmount(
        tx as PrismaService,
        reqUser,
        body
      );
    });
    return true;
  }

  // 1. 根据待删除的关系数据更新明细（金额相减、数量相减）
  // 2. 删除关系数据
  // 3. 删除无关系的明细数据
  // 4. 更新主表金额相关数据
  async removeSettlementDetails(
    reqUser: IReqUser,
    body: SumReceivingReturnBodyDto
  ) {
    await this.prisma.$transaction(async (tx) => {
      // 根据待删除的关系数据更新明细（金额相减、数量相减）
      await this.repository.updateMaterialSettlementDetail(
        tx as PrismaService,
        reqUser,
        body
      );
      // 删除关系数据
      await tx.materialSettlementBillRefDetail.updateMany({
        where: {
          settlementId: body.settlementId,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          billId: {
            in: body.receivingReturnIds
          }
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
      // 删除无关系的明细数据
      await this.repository.removeMaterialSettlementDetail(
        tx as PrismaService,
        reqUser,
        body
      );
      // 更新主表数据
      await this.repository.updateMaterialSettlementAmount(
        tx as PrismaService,
        reqUser,
        body
      );
    });
    return true;
  }

  async createSettlementDetail(
    reqUser: IReqUser,
    body: MaterialSettlementDetailResponseDto
  ) {
    const userId = reqUser.id;
    await this.prisma.materialSettlementDetail.create({
      data: {
        settlementId: body.settlementId,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        createBy: userId,
        updateBy: userId,
        materialId: null,
        materialName: body.materialName,
        materialSpec: body.materialSpec,
        unit: body.unit,
        quantity: null,
        price: null,
        amount: null,
        settlementQuantity: null,
        settlementPrice: null,
        settlementAmount: null,
        isAutoCalculation: true,
        isManual: true,
        businessCostSubjectDetailId: null
      }
    });
    return true;
  }

  async getSettlementDetails(
    reqUser: IReqUser,
    query: MaterialSettlementDetailQueryDto
  ) {
    const items = await this.repository.selectMaterialSettlementDetails(
      reqUser,
      query
    );

    return items;
  }

  async move(reqUser: IReqUser, fromId: string, toId: string) {
    await CommonRepositories.changeDataOrderNo(this.prisma, {
      tenantId: reqUser.tenantId,
      orgId: reqUser.orgId,
      fromId,
      toId,
      tableName: 'material_settlement_detail'
    });
    return true;
  }

  async getMaterialReceivingReturnDetails(
    reqUser: IReqUser,
    query: MaterialReceivingReturnDetailQueryDto
  ) {
    const result = await this.repository.selectMaterialReceivingReturnDetails(
      reqUser,
      query
    );
    return result;
  }

  async updateSettlementDetail(
    reqUser: IReqUser,
    body: MaterialSettlementDetailResponseDto
  ) {
    const userId = reqUser.id;

    await this.prisma.$transaction(async (tx) => {
      await tx.materialSettlementDetail.update({
        where: {
          id: body.id,
          settlementId: body.settlementId,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        data: {
          ...body,
          updateBy: userId
        }
      });
      // 更新主表数据
      await this.repository.updateMaterialSettlementAmount(
        tx as PrismaService,
        reqUser,
        {
          settlementId: body.settlementId,
          receivingReturnIds: []
        }
      );
    });

    return true;
  }

  async deleteSettlementDetail(id: string, reqUser: IReqUser) {
    const item = await this.prisma.materialSettlementDetail.findFirst({
      where: {
        id,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isManual: true,
        isDeleted: false
      }
    });

    if (!item) {
      return true;
    }

    await this.prisma.$transaction(async (tx) => {
      await tx.materialSettlementDetail.update({
        where: {
          id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
      // 更新主表数据
      await this.repository.updateMaterialSettlementAmount(
        tx as PrismaService,
        reqUser,
        {
          settlementId: item.settlementId,
          receivingReturnIds: []
        }
      );
    });
    return true;
  }
}
