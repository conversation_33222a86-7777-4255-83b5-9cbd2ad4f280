import { ApiProperty, PickType } from '@nestjs/swagger';
import {
  IsBoolean,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString
} from 'class-validator';

import {
  AuditStatus,
  PurchaseType,
  SettlementBillType,
  SubmitStatus
} from '@/prisma/generated';

class ContractItemDto {
  @ApiProperty({ description: '合同id' })
  @IsString()
  contractId: string;

  @ApiProperty({ description: '合同名称' })
  @IsString()
  contractName: string;

  @ApiProperty({ description: '合同签订金额' })
  @IsNumber()
  contractAmount?: number;

  @ApiProperty({ description: '价格类型' })
  @IsString()
  priceType?: string;

  @ApiProperty({ description: '税率' })
  @IsNumber()
  taxRate?: number;
}

export class TransferInProjectResponseDto {
  @ApiProperty({ description: 'projectId' })
  @IsNotEmpty({ message: 'projectId不能为空' })
  @IsString({ message: 'projectId必须是字符串' })
  projectId: string;

  @ApiProperty({ description: 'projectName' })
  @IsNotEmpty({ message: 'projectName不能为空' })
  @IsString({ message: 'projectName必须是字符串' })
  projectName?: string;

  @ApiProperty({ description: 'settlementId' })
  @IsString({ message: 'settlementId必须是字符串' })
  settlementId?: string;
}

export class SettlementSupplierAndContractResponseDto extends PickType(
  ContractItemDto,
  ['contractId', 'contractName']
) {
  @ApiProperty({ description: '供应商id' })
  @IsString()
  supplierId: string;

  @ApiProperty({ description: '供应商名称' })
  @IsString()
  supplierName: string;
}

export class QueryReceivingReturnDto extends PickType(
  SettlementSupplierAndContractResponseDto,
  ['contractId', 'supplierId']
) {
  @ApiProperty({ description: 'settlementId' })
  @IsNotEmpty({ message: 'settlementId不能为空' })
  @IsString({ message: 'settlementId必须是字符串' })
  settlementId: string;
}

export class ReceivingReturnResponseDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '单据编码' })
  @IsNotEmpty({ message: '单据编码不能为空' })
  @IsString({ message: '单据编码必须是字符串' })
  code: string;

  @ApiProperty({ description: '采购类型' })
  @IsOptional({ message: '采购类型可以为空' })
  @IsIn(Object.values(PurchaseType), {
    message: '采购类型必须是有效枚举值'
  })
  @IsString({ message: '采购类型必须是字符串' })
  purchaseType?: PurchaseType;

  @ApiProperty({ description: '材料类别' })
  materialCategories: string;

  @ApiProperty({ description: '不含税金额' })
  @IsOptional({ message: '不含税金额可以为空' })
  @IsNumber({}, { message: '不含税金额必须是数字' })
  taxExcludedAmount?: number;

  @ApiProperty({ description: '提交状态' })
  @IsNotEmpty({ message: '提交状态不能为空' })
  @IsIn(Object.values(SubmitStatus), {
    message: '提交状态必须是有效枚举值'
  })
  @IsString({ message: '提交状态必须是字符串' })
  submitStatus: SubmitStatus;

  @ApiProperty({ description: '审批状态' })
  @IsNotEmpty({ message: '审批状态不能为空' })
  @IsIn(Object.values(AuditStatus), {
    message: '审批状态必须是有效枚举值'
  })
  @IsString({ message: '审批状态必须是字符串' })
  auditStatus: AuditStatus;

  @ApiProperty({ description: '结算单据类型' })
  @IsNotEmpty({ message: '结算单据类型不能为空' })
  @IsIn(Object.values(SettlementBillType), {
    message: '结算单据类型必须是有效枚举值'
  })
  @IsString({ message: '结算单据类型必须是字符串' })
  settlementBillType: SettlementBillType;

  @ApiProperty({ description: '创建人名称' })
  @IsNotEmpty({ message: '创建人名称不能为空' })
  @IsString({ message: '创建人名称必须是字符串' })
  creator: string;

  @ApiProperty({ description: '单据日期（收料或退货）' })
  billDate: string;
}

export class MaterialSettlementDetailQueryDto {
  @ApiProperty({ description: 'settlementId' })
  @IsNotEmpty({ message: 'settlementId不能为空' })
  @IsString({ message: 'settlementId必须是字符串' })
  settlementId: string;
}

export class SumReceivingReturnBodyDto extends PickType(
  MaterialSettlementDetailQueryDto,
  ['settlementId']
) {
  @ApiProperty({ description: '收料单或退货单id列表' })
  @IsNotEmpty({ message: '收料单或退货单id列表不能为空' })
  @IsString({ each: true, message: '收料单或退货单id必须是字符串' })
  receivingReturnIds: string[];
}

export class SumReceivingReturnResponseDto {
  @ApiProperty({ description: '材料id' })
  @IsNotEmpty({ message: '材料id不能为空' })
  @IsString({ message: '材料id必须是字符串' })
  materialId: string;

  @ApiProperty({ description: '材料名称' })
  @IsNotEmpty({ message: '材料名称不能为空' })
  @IsString({ message: '材料名称必须是字符串' })
  materialName: string;

  @ApiProperty({ description: '材料规格' })
  @IsNotEmpty({ message: '材料规格不能为空' })
  @IsString({ message: '材料规格必须是字符串' })
  materialSpec: string;

  @ApiProperty({ description: '计量单位' })
  @IsOptional({ message: '计量单位可以为空' })
  @IsString({ message: '计量单位必须是字符串' })
  unit?: string;

  @ApiProperty({ description: '收料数量' })
  @IsNotEmpty({ message: '收料数量不能为空' })
  @IsNumber({}, { message: '收料数量必须是数字' })
  quantity: number;

  @ApiProperty({ description: '不含税单价' })
  @IsOptional({ message: '不含税单价可以为空' })
  @IsNumber({}, { message: '不含税单价必须是数字' })
  priceExcludingTax?: number | null;

  @ApiProperty({ description: '结算单价' })
  @IsOptional({ message: '结算单价可以为空' })
  @IsNumber({}, { message: '结算单价必须是数字' })
  settlementPrice?: number | null;

  @ApiProperty({ description: '不含税金额' })
  @IsNotEmpty({ message: '不含税金额不能为空' })
  @IsNumber({}, { message: '不含税金额必须是数字' })
  taxExcludedAmount: number;

  @ApiProperty({ description: '备注' })
  @IsOptional({ message: '备注可以为空' })
  @IsString({ message: '备注必须是字符串' })
  remark?: string | null;
}

export class MaterialSettlementDetailResponseDto extends PickType(
  SumReceivingReturnResponseDto,
  [
    'materialId',
    'materialName',
    'materialSpec',
    'unit',
    'quantity',
    'settlementPrice',
    'remark'
  ]
) {
  @ApiProperty({ description: 'id' })
  @IsOptional({ message: 'id可以为空' })
  @IsString({ message: 'id必须是字符串' })
  id?: string;

  @ApiProperty({ description: '收料单价' })
  @IsOptional({ message: '收料单价可以为空' })
  @IsNumber({}, { message: '收料单价必须是数字' })
  price?: number | null;

  @ApiProperty({ description: '收料金额' })
  @IsNotEmpty({ message: '收料金额不能为空' })
  @IsNumber({}, { message: '收料金额必须是数字' })
  amount: number;

  @ApiProperty({ description: '结算数量' })
  @IsNotEmpty({ message: '结算数量不能为空' })
  @IsNumber({}, { message: '结算数量必须是数字' })
  settlementQuantity: number;

  @ApiProperty({ description: '结算金额' })
  @IsNotEmpty({ message: '结算金额不能为空' })
  @IsNumber({}, { message: '结算金额必须是数字' })
  settlementAmount: number;

  @ApiProperty({ description: '排序号' })
  @IsNotEmpty({ message: '排序号不能为空' })
  @IsNumber({}, { message: '排序号必须是数字' })
  orderNo: number;

  @ApiProperty({ description: '结算单ID' })
  @IsString()
  settlementId: string;

  @ApiProperty({ description: '是否根据单价及数量自动计算金额' })
  @IsBoolean()
  isAutoCalculation: boolean;

  @ApiProperty({ description: '是否手动新增' })
  @IsBoolean()
  isManual: boolean;

  @ApiProperty({ description: '业务成本科目明细' })
  @IsString()
  businessCostSubjectDetailId?: string;
}

export class SettlementBillRefDto {
  @ApiProperty({ description: '结算单据类型' })
  @IsNotEmpty({ message: '结算单据类型不能为空' })
  @IsIn(Object.values(SettlementBillType), {
    message: '结算单据类型必须是有效枚举值'
  })
  @IsString({ message: '结算单据类型必须是字符串' })
  settlementBillType: SettlementBillType;

  @ApiProperty({ description: '单据ID' })
  billId: string;

  @ApiProperty({ description: '单据明细ID' })
  billDetailId: string;

  @ApiProperty({ description: '结算单明细ID' })
  @IsString()
  settlementDetailId: string;

  @ApiProperty({ description: '结算单ID' })
  @IsString()
  settlementId: string;

  @ApiProperty({ description: '租户ID' })
  tenantId: string;

  @ApiProperty({ description: '组织ID' })
  orgId: string;
}

export class MaterialReceivingReturnDetailQueryDto extends PickType(
  SettlementBillRefDto,
  ['settlementId', 'settlementDetailId']
) {}

export class MaterialReceivingReturnDetailResponseDto extends PickType(
  SumReceivingReturnResponseDto,
  ['unit', 'quantity', 'settlementPrice', 'taxExcludedAmount']
) {
  @ApiProperty({ description: '结算单据类型' })
  @IsNotEmpty({ message: '结算单据类型不能为空' })
  @IsIn(Object.values(SettlementBillType), {
    message: '结算单据类型必须是有效枚举值'
  })
  @IsString({ message: '结算单据类型必须是字符串' })
  settlementBillType: SettlementBillType;

  @ApiProperty({ description: '单据ID' })
  billId: string;

  @ApiProperty({ description: '单据明细ID' })
  billDetailId: string;

  @ApiProperty({ description: '单据编码' })
  @IsNotEmpty({ message: '单据编码不能为空' })
  @IsString({ message: '单据编码必须是字符串' })
  code: string;

  @ApiProperty({ description: '单据日期（收料或退货）' })
  billDate: string;

  @ApiProperty({ description: '创建人名称' })
  @IsNotEmpty({ message: '创建人名称不能为空' })
  @IsString({ message: '创建人名称必须是字符串' })
  creator: string;
}
