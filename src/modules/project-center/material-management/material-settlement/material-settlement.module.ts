import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';
import { PlatformModule } from '@/modules/platform/platform.module';

import { MaterialSettlementController } from './material-settlement/material-settlement.controller';
import { MaterialSettlementRepository } from './material-settlement/material-settlement.repository';
import { MaterialSettlementService } from './material-settlement/material-settlement.service';
import { MaterialSettlementAttachmentController } from './material-settlement-attachment/material-settlement-attachment.controller';
import { MaterialSettlementAttachmentService } from './material-settlement-attachment/material-settlement-attachment.service';
import { MaterialSettlementDetailController } from './material-settlement-detail/material-settlement-detail.controller';
import { MaterialSettlementDetailRepository } from './material-settlement-detail/material-settlement-detail.repository';
import { MaterialSettlementDetailService } from './material-settlement-detail/material-settlement-detail.service';

@Module({
  imports: [PrismaModule, PlatformModule],
  controllers: [
    MaterialSettlementController,
    MaterialSettlementAttachmentController,
    MaterialSettlementDetailController
  ],
  providers: [
    MaterialSettlementService,
    MaterialSettlementRepository,
    MaterialSettlementAttachmentService,
    MaterialSettlementDetailService,
    MaterialSettlementDetailRepository
  ],
  exports: [MaterialSettlementRepository]
})
export class MaterialSettlementModule {}
