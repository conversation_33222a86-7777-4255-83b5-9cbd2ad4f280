import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import {
  AuditStatus,
  MaterialSettlementStatus,
  Prisma,
  SettlementBillType,
  SubmitStatus
} from '@/prisma/generated';

import { MaterialSettlementPeriodResDto } from './material-settlement.dto';

@Injectable()
export class MaterialSettlementRepository {
  constructor(private readonly prisma: PrismaService) {}

  // 获取物资结算单统计周期
  async selectSettlementPeriods(reqUser: IReqUser) {
    const result = await this.prisma.$queryRaw<any[]>`
        select distinct
        year, month
      from material_settlement
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and org_id = ${reqUser.orgId}
      order by year desc, month desc
    `;

    return result;
  }

  async getSettlementPeriodAmounts(
    reqUser: IReqUser,
    query: MaterialSettlementPeriodResDto
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
        select 
        sum(tax_included_amount) as cumulation_tax_included_amount,
        SUM(tax_included_amount) FILTER (WHERE ${query.year && query.month ? Prisma.sql` (year * 100 + month) = ${query.year * 100 + query.month}` : Prisma.sql` 1=1`}) AS tax_included_amount
      from material_settlement
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and org_id = ${reqUser.orgId}
         ${query.year && query.month ? Prisma.sql`and (year * 100 + month) <= ${query.year * 100 + query.month}` : Prisma.empty}
    `;

    return result;
  }

  async selectSettlementPeriodContracts(
    reqUser: IReqUser,
    query: MaterialSettlementPeriodResDto
  ) {
    const { year, month } = query;
    const result = await this.prisma.$queryRaw<any[]>`
      select 
        count(distinct contract_id) as count
      from material_settlement
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and org_id = ${reqUser.orgId}
        and submit_status = ${SubmitStatus.SUBMITTED}::"SubmitStatus"
        and year = ${year}
        and month = ${month}
    `;

    return result;
  }

  async selectUnSettlementPeriodContracts(
    reqUser: IReqUser,
    { year, month, day }: { year: number; month: number; day: number }
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
    with settled_contract as (
      select 
        contract_id
      from material_settlement
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and org_id = ${reqUser.orgId}
        and submit_status = ${SubmitStatus.SUBMITTED}::"SubmitStatus"
        and year = ${year}
        and month = ${month}
    ),
    settled_receiving as (
      select
        distinct a.bill_id
      from material_settlement_bill_ref_detail a
      join material_settlement b
        on a.tenant_id = b.tenant_id
        and a.org_id = b.org_id
        and a.settlement_id = b.id
      where a.is_deleted = false
        and a.tenant_id = ${reqUser.tenantId}
        and a.org_id = ${reqUser.orgId}
        and a.settlement_bill_type = ${SettlementBillType.RECEIVING}::"SettlementBillType"
        and b.is_deleted = false
        and b.submit_status = ${SubmitStatus.SUBMITTED}::"SubmitStatus"
    )
      select 
        count(distinct contract_id) as count
      from material_receiving
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and org_id = ${reqUser.orgId}
        and audit_status = ${AuditStatus.APPROVED}::"AuditStatus"
        and (year * 10000 + month * 100 + day) <= ${year * 10000 + month * 100 + day}
        and contract_id not in (select contract_id from settled_contract)
        and id not in (select bill_id from settled_receiving)
    `;

    return result;
  }

  async selectSettlementPeriodMaterials(
    reqUser: IReqUser,
    query: MaterialSettlementPeriodResDto
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
    with material_category_settlement as (
      select
        case
          when position('|' in mdc.full_name) > 0
          then split_part(mdc.full_name, '|', 2)
          else split_part(mdc.full_name, '|', 1)
        end as material_category_name,
        case
          when position('|' in mdc.full_id) > 0
          then split_part(mdc.full_id, '|', 2)
          else split_part(mdc.full_id, '|', 1)
        end as material_category_id,
        b.year,
        b.month,
        a.settlement_amount * (1 + coalesce(b.tax_rate, 0))  as tax_included_amount
      from material_settlement_detail a
      join material_settlement b
        on a.tenant_id = b.tenant_id
        and a.org_id = b.org_id
        and a.settlement_id = b.id
      join material_dictionary_detail mdd
        on mdd.id = a.material_id
        and mdd.is_deleted = false
        and mdd.tenant_id = ${reqUser.tenantId}
      join material_dictionary_category mdc
        on mdc.id = mdd.material_dictionary_category_id
        and mdc.is_deleted = false
        and mdc.tenant_id = ${reqUser.tenantId}
        and mdc.org_id = mdd.org_id
      where a.is_deleted = false
        and a.tenant_id = ${reqUser.tenantId}
        and a.org_id = ${reqUser.orgId}
        and b.is_deleted = false
        ${query.year && query.month ? Prisma.sql`and (b.year * 100 + b.month) <= ${query.year * 100 + query.month}` : Prisma.empty}
    )
    select 
      material_category_name,
      material_category_id,
      sum(tax_included_amount) as cumulation_tax_included_amount,
      sum(tax_included_amount) filter (where ${query.year && query.month ? Prisma.sql`(year * 100 + month) = ${query.year * 100 + query.month}` : Prisma.sql`1=1`}) as tax_included_amount
    from material_category_settlement
    group by material_category_name, material_category_id
    order by material_category_id
    `;

    return result;
  }

  async selectMaterialSettlementList(
    reqUser: IReqUser,
    query: MaterialSettlementPeriodResDto
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
    with material_category_settlement as (
      select
        case
          when position('|' in mdc.full_id) > 0
          then split_part(mdc.full_id, '|', 2)
          else split_part(mdc.full_id, '|', 1)
        end as material_category_id,
        a.settlement_id
      from material_settlement_detail a
      join material_settlement b
        on a.tenant_id = b.tenant_id
        and a.org_id = b.org_id
        and a.settlement_id = b.id
      join material_dictionary_detail mdd
        on mdd.id = a.material_id
        and mdd.is_deleted = false
        and mdd.tenant_id = ${reqUser.tenantId}
      join material_dictionary_category mdc
        on mdc.id = mdd.material_dictionary_category_id
        and mdc.is_deleted = false
        and mdc.tenant_id = ${reqUser.tenantId}
        and mdc.org_id = mdd.org_id
      where a.is_deleted = false
        and a.tenant_id = ${reqUser.tenantId}
        and a.org_id = ${reqUser.orgId}
        and b.is_deleted = false
        ${query.year && query.month ? Prisma.sql`and (b.year * 100 + b.month) <= ${query.year * 100 + query.month}` : Prisma.empty}
    )
    select
      COALESCE(b.simple_name, c.company_version) as new_supplier_name,
      a.id,
      a.year,
      a.month,
      a.code,
      a.settlement_type,
      a.supplier_id,
      a.supplier_name,
      a.contract_id,
      a.contract_name,
      a.contract_amount,
      a.price_type,
      a.tax_rate,
      a.tax_excluded_amount,
      a.tax_included_amount,
      a.tax_amount,
      a.before_cumulation_tax_excluded_amount,
      a.before_cumulation_tax_included_amount,
      a.before_cumulation_tax_amount,
      TO_CHAR(a.settlement_date, 'YYYY-MM-DD') AS settlement_date,
      a.submit_status,
      a.audit_status,
      u.nickname as creator,
      o.seal_name as project_name,
      o2.seal_name as transfer_in_project_name
    from material_settlement a
    left join supplier_directory b
      on a.tenant_id = b.tenant_id
      and a.supplier_id = b.id
      and b.is_deleted = false
    left join business_base_info c
      on c.tenant_id = a.tenant_id
      and c.id = a.supplier_id
      and c.is_deleted = false
    left join platform_meta."user" u
      on u.id = a.create_by
    left join platform_meta."org" o
      on o.id = a.org_id
      and o.tenant_id = a.tenant_id
    left join platform_meta."org" o2
      on o2.id = a.transfer_in_project_id
      and o2.tenant_id = a.tenant_id
    where a.is_deleted = false
      and a.tenant_id = ${reqUser.tenantId}
      and a.org_id = ${reqUser.orgId}
      ${query.supplierId ? Prisma.sql`and a.supplier_id = ${query.supplierId}` : Prisma.empty}
      ${query.contractId ? Prisma.sql`and a.contract_id = ${query.contractId}` : Prisma.empty}
      ${query.id ? Prisma.sql`and a.id = ${query.id}` : Prisma.empty}
      ${query.year && query.month ? Prisma.sql`and (a.year * 100 + a.month) ${query.isCumulation ? Prisma.sql`<=` : Prisma.sql`=`} ${query.year * 100 + query.month}` : Prisma.empty}
      ${query.materialCategoryId ? Prisma.sql`and a.id in (select distinct settlement_id from material_category_settlement where material_category_id = ${query.materialCategoryId}) ` : Prisma.empty}
      order by a.settlement_date desc, a.id desc
    `;

    return result;
  }

  async selectMaterialContractInvoiceType(
    reqUser: IReqUser,
    contractId: string
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
      select 
        mcfr.text_value as value 
      from material_contract_field_rule mcfr
      join contract_template_field_rule ctfr
        on ctfr.id = mcfr.contract_template_field_rule_id
        and ctfr.contract_template_id = mcfr.contract_template_id
        and ctfr.is_deleted = false
      join field_rule fr
        on fr.code = '发票类型' 
        and fr.id = ctfr.field_rule_id 
        and fr.is_deleted = false
      where mcfr.material_contract_id = ${contractId}
        and mcfr.org_id  = ${reqUser.orgId}
        and mcfr.tenant_id = ${reqUser.tenantId}
        and mcfr.is_deleted = false
    `;

    return result;
  }

  async selectMaterialContract(
    reqUser: IReqUser,
    contractId: string,
    settlementDate: Date
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
    with contract as (
      select 
        sign_date,
        amount as contract_amount,
        price_type,
        tax_rate,
        id
      from material_contract
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and org_id = ${reqUser.orgId}
        and id = ${contractId}
        and sign_date <= ${settlementDate}
      union all
      select 
        sign_date,
        amount as contract_amount,
        price_type,
        tax_rate,
        id
      from material_contract
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and org_id = ${reqUser.orgId}
        and parent_id = ${contractId}
        and sign_date <= ${settlementDate}
        and audit_status = 'APPROVED'
    )
    select 
      *
    from contract
    order by sign_date desc
    `;

    return result;
  }

  async updateMaterialReceivingSettlementStatus(
    tx: PrismaService,
    reqUser: IReqUser,
    id: string,
    settlementStatus: MaterialSettlementStatus
  ) {
    await tx.$executeRaw<any[]>`
    with bill as (
      select 
        distinct bill_id
      from material_settlement_bill_ref_detail
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and org_id = ${reqUser.orgId}
        and settlement_id = ${id}
        and settlement_bill_type = 'RECEIVING'
    )
    update material_receiving a
      set material_settlement_status = ${settlementStatus}::"MaterialSettlementStatus",
        update_by = ${reqUser.id},
        update_at = NOW()
    from bill b
    where a.is_deleted = false
      and a.tenant_id = ${reqUser.tenantId}
      and a.org_id = ${reqUser.orgId}
      and a.id = b.bill_id
    `;

    return true;
  }

  async updateMaterialReturnSettlementStatus(
    tx: PrismaService,
    reqUser: IReqUser,
    id: string,
    settlementStatus: MaterialSettlementStatus
  ) {
    await tx.$executeRaw<any[]>`
    with bill as (
      select 
        distinct bill_id
      from material_settlement_bill_ref_detail
      where is_deleted = false
        and tenant_id = ${reqUser.tenantId}
        and org_id = ${reqUser.orgId}
        and settlement_id = ${id}
        and settlement_bill_type = 'RETURN_SALES'
    )
    update material_return_sales_form a
      set material_settlement_status = ${settlementStatus}::"MaterialSettlementStatus",
        update_by = ${reqUser.id},
        update_at = NOW()
    from bill b
    where a.is_deleted = false
      and a.tenant_id = ${reqUser.tenantId}
      and a.org_id = ${reqUser.orgId}
      and a.id = b.bill_id
    `;

    return true;
  }
}
