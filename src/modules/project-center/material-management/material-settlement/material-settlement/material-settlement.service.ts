import { add } from '@ewing/infra-cloud-sdk';
import { BadRequestException, Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { PlatformService } from '@/modules/platform/platform.service';
import type { MaterialSettlement } from '@/prisma/generated';
import {
  AuditStatus,
  MaterialSettlementStatus,
  SubmitStatus
} from '@/prisma/generated';

import {
  MaterialSettlementCreateDto,
  MaterialSettlementPeriodResDto,
  MaterialSettlementSubmitBodyDto,
  MaterialSettlementUpdateDto
} from './material-settlement.dto';
import { MaterialSettlementRepository } from './material-settlement.repository';

@Injectable()
export class MaterialSettlementService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly repository: MaterialSettlementRepository,
    private readonly platformService: PlatformService
  ) {}

  async getSettlementPeriods(
    reqUser: IReqUser
  ): Promise<MaterialSettlementPeriodResDto[]> {
    const result = await this.repository.selectSettlementPeriods(reqUser);
    return result;
  }

  async getSettlementPeriodAmounts(
    reqUser: IReqUser,
    query: MaterialSettlementPeriodResDto
  ) {
    const [result] = await this.repository.getSettlementPeriodAmounts(
      reqUser,
      query
    );
    return result;
  }

  async getSettlementPeriodContracts(req: Request, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    // 当前时间的上一个账期
    const curDate = new Date();
    const curPeriod = await this.platformService.getOrgPeriod(
      req,
      tenantId,
      orgId,
      curDate
    );
    const lastPeriodEndDate = dayjs(curPeriod.startDate).subtract(1, 'day');
    const lastPeriod = await this.platformService.getOrgPeriod(
      req,
      tenantId,
      orgId,
      lastPeriodEndDate.toDate()
    );
    const year = lastPeriod.year;
    const month = lastPeriod.month;
    const day = dayjs(lastPeriod.endDate).date();

    const [settledRet] = await this.repository.selectSettlementPeriodContracts(
      reqUser,
      {
        year,
        month
      }
    );

    const [unSettledRet] =
      await this.repository.selectUnSettlementPeriodContracts(reqUser, {
        year,
        month,
        day
      });

    const settledCount = Number(settledRet.count);
    const unSettledCount = Number(unSettledRet.count);
    return {
      allCount: settledCount + unSettledCount,
      settledCount,
      unSettledCount,
      year,
      month
    };
  }

  async getSettlementPeriodMaterials(
    reqUser: IReqUser,
    query: MaterialSettlementPeriodResDto
  ) {
    const result = await this.repository.selectSettlementPeriodMaterials(
      reqUser,
      query
    );
    return result;
  }

  async getMaterialSettlementList(
    reqUser: IReqUser,
    query: MaterialSettlementPeriodResDto
  ) {
    const result = await this.repository.selectMaterialSettlementList(
      reqUser,
      query
    );
    return result;
  }

  async addMaterialSettlement(
    req: Request,
    reqUser: IReqUser,
    body: MaterialSettlementCreateDto
  ) {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;
    const day = currentDate.getDate();
    // 获取当前收料单的最新的收料单编号
    const code = await this.getMaxCode(reqUser, year, month);
    const curPeriod = await this.platformService.getOrgPeriod(
      req,
      reqUser.tenantId,
      reqUser.orgId,
      currentDate,
      true
    );
    const ret = await this.prisma.materialSettlement.create({
      data: {
        year: curPeriod.year,
        month: curPeriod.month,
        code: code,
        settlementType: body.settlementType,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        createBy: reqUser.id,
        updateBy: reqUser.id,
        settlementDate: dayjs(`${year}-${month}-${day}`)
          .add(8, 'hour')
          .toISOString()
      }
    });
    const [billItem] = await this.getMaterialSettlementList(reqUser, {
      id: ret.id
    });
    return billItem;
  }

  // 获取当前物资结算单的最新编号
  async getMaxCode(
    reqUser: IReqUser,
    year: number,
    month: number
  ): Promise<string> {
    const { tenantId, orgId } = reqUser;
    const code = ['结', `${year}${String(month).padStart(2, '0')}`, '001'];
    const maxCode = await this.prisma.materialSettlement.findFirst({
      where: {
        orgId,
        tenantId,
        isDeleted: false,
        code: {
          startsWith: [code[0], code[1]].join('-')
        }
      },
      orderBy: {
        code: 'desc'
      }
    });
    if (maxCode?.code.split('-')[1] === code[1]) {
      const lastCode = maxCode.code;
      const lastCodeNumber = parseInt(lastCode.split('-')[2], 10);
      code[2] = String(lastCodeNumber + 1).padStart(3, '0');
    }
    return code.join('-');
  }

  async getBeforeContractSettlement(
    id: string,
    reqUser: IReqUser,
    data: MaterialSettlementUpdateDto
  ) {
    if (!data.contractId) {
      return null;
    }
    const beforeContractSettlement =
      await this.prisma.materialSettlement.findFirst({
        where: {
          contractId: data.contractId,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          isDeleted: false,
          id: {
            not: id
          }
        },
        orderBy: [
          {
            settlementDate: 'desc'
          },
          {
            code: 'desc'
          }
        ]
      });
    return beforeContractSettlement;
  }

  async checkUpdateSupplierOrContract(
    id: string,
    reqUser: IReqUser,
    data: MaterialSettlementUpdateDto,
    beforeContractSettlement: MaterialSettlement | null
  ) {
    if (data.contractId) {
      if (beforeContractSettlement) {
        if (
          beforeContractSettlement.submitStatus !== SubmitStatus.SUBMITTED ||
          beforeContractSettlement.auditStatus !== AuditStatus.APPROVED
        ) {
          throw new BadRequestException(
            '存在未提交或未审批的结算单，请先完成上一份审批！'
          );
        }
        data.beforeCumulationTaxExcludedAmount = add(
          beforeContractSettlement.taxExcludedAmount || 0,
          beforeContractSettlement.beforeCumulationTaxExcludedAmount || 0
        );
        data.beforeCumulationTaxIncludedAmount = add(
          beforeContractSettlement.taxIncludedAmount || 0,
          beforeContractSettlement.beforeCumulationTaxIncludedAmount || 0
        );

        data.beforeCumulationTaxAmount = add(
          beforeContractSettlement.taxAmount || 0,
          beforeContractSettlement.beforeCumulationTaxAmount || 0
        );
      } else {
        data.beforeCumulationTaxExcludedAmount = null;
        data.beforeCumulationTaxIncludedAmount = null;
        data.beforeCumulationTaxAmount = null;
      }
    }

    const detailItem = await this.prisma.materialSettlementDetail.findFirst({
      where: {
        settlementId: id,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      }
    });
    if (detailItem) {
      throw new BadRequestException('明细已有数据，不允许修改');
    }
    return true;
  }

  async mixinMaterialContract(
    data: MaterialSettlementUpdateDto,
    reqUser: IReqUser
  ) {
    if (data.contractId) {
      const [contractInvoiceType] =
        await this.repository.selectMaterialContractInvoiceType(
          reqUser,
          data.contractId
        );
      const [contractItem] = await this.repository.selectMaterialContract(
        reqUser,
        data.contractId,
        dayjs(data.settlementDate).toDate()
      );
      data.taxRate =
        contractInvoiceType?.value === '增值税专用发票'
          ? contractItem?.taxRate || null
          : null;
      data.priceType = contractItem?.priceType || null;
      data.contractAmount = contractItem?.contractAmount || null;
    }

    return data;
  }

  async updateMaterialSettlement(
    id: string,
    req: Request,
    reqUser: IReqUser,
    data: MaterialSettlementUpdateDto
  ) {
    const [billItem] = await this.getMaterialSettlementList(reqUser, { id });
    if (billItem.submitStatus === SubmitStatus.SUBMITTED) {
      throw new BadRequestException('已提交的结算单，不能修改');
    }
    const beforeContractSettlement = await this.getBeforeContractSettlement(
      id,
      reqUser,
      data
    );
    if (
      data.supplierId !== billItem.supplierId ||
      data.contractId !== billItem.contractId
    ) {
      await this.checkUpdateSupplierOrContract(
        id,
        reqUser,
        data,
        beforeContractSettlement
      );
    }

    if (
      beforeContractSettlement &&
      dayjs(
        dayjs(beforeContractSettlement.settlementDate).format('YYYY-MM-DD')
      ).isAfter(data.settlementDate)
    ) {
      throw new BadRequestException(
        '不允许选择已有结算日期之前的结算日期，请重新选择！'
      );
    }

    // 更新合同时，获取对应合同签订金额、价格类型及税率
    if (data.contractId !== billItem.contractId) {
      if (data.contractId) {
        await this.mixinMaterialContract(data, reqUser);
      } else {
        data.contractId = null;
        data.contractName = null;
        data.taxRate = null;
        data.priceType = null;
        data.contractAmount = null;
        data.beforeCumulationTaxExcludedAmount = null;
        data.beforeCumulationTaxIncludedAmount = null;
        data.beforeCumulationTaxAmount = null;
      }
    }

    // 更新结算日期时，重新计算账期
    if (data.settlementDate !== billItem.settlementDate) {
      const settlementDate = dayjs(data.settlementDate).toDate();
      const oldSettlementDate = dayjs(billItem.settlementDate).toDate();
      const curPeriod = await this.platformService.getOrgPeriod(
        req,
        reqUser.tenantId,
        reqUser.orgId,
        settlementDate
      );
      data.year = curPeriod.year;
      data.month = curPeriod.month;
      // 重新获取税率相关信息
      await this.mixinMaterialContract(data, reqUser);

      const year = settlementDate.getFullYear();
      const month = settlementDate.getMonth() + 1;
      const oldYear = oldSettlementDate.getFullYear();
      const oldMonth = oldSettlementDate.getMonth() + 1;

      if (year !== oldYear || month !== oldMonth) {
        data.code = await this.getMaxCode(reqUser, year, month);
      }
    }

    await this.prisma.materialSettlement.update({
      where: {
        id,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      },
      data: {
        ...data,
        settlementDate: dayjs(data.settlementDate).add(8, 'hour').toISOString(),
        updateBy: reqUser.id
      }
    });
    return true;
  }

  /**
   * 删除结算单
   * @param id
   * @param reqUser
   */
  async deleteMaterialSettlement(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;
    // 删除前校验
    await this.beforeDelete(id, reqUser);
    await this.prisma.$transaction(async (tx) => {
      await tx.materialSettlement.update({
        where: {
          id,
          orgId,
          tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
      await tx.materialSettlementDetail.updateMany({
        where: {
          settlementId: id,
          orgId,
          tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
      await tx.materialSettlementBillRefDetail.updateMany({
        where: {
          settlementId: id,
          orgId,
          tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
      await tx.materialSettlementAttachment.updateMany({
        where: {
          settlementId: id,
          orgId,
          tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
    });
    return true;
  }

  /**
   * 删除前校验
   * @param id
   * @param reqUser
   */
  async beforeDelete(id: string, reqUser: IReqUser) {
    const [billItem] = await this.getMaterialSettlementList(reqUser, { id });
    if (billItem.submitStatus !== SubmitStatus.PENDING) {
      throw new BadRequestException('已提交的单据，不能删除');
    }
  }

  /**
   * 修改提交状态
   * @param id
   * @param reqUser
   * @param data
   */
  async updateSubmitStatus(
    id: string,
    reqUser: IReqUser,
    body: MaterialSettlementSubmitBodyDto
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    const { submitStatus } = body;
    const data: {
      submitStatus: SubmitStatus;
      updateBy: string;
    } = {
      submitStatus,
      updateBy: userId
    };

    let settlementStatus: MaterialSettlementStatus =
      MaterialSettlementStatus.UN_SETTLED;
    if (submitStatus === SubmitStatus.SUBMITTED) {
      settlementStatus = MaterialSettlementStatus.SETTLED;
    }

    // 提交状态变更校验逻辑
    await this.beforeUpdateSubmitStatus(id, reqUser, submitStatus);
    await this.prisma.$transaction(async (tx) => {
      await tx.materialSettlement.update({
        where: {
          id,
          orgId,
          tenantId,
          isDeleted: false
        },
        data
      });
      // 更新关联单据结算状态
      await this.repository.updateMaterialReceivingSettlementStatus(
        tx as PrismaService,
        reqUser,
        id,
        settlementStatus
      );
      await this.repository.updateMaterialReturnSettlementStatus(
        tx as PrismaService,
        reqUser,
        id,
        settlementStatus
      );
    });

    return true;
  }

  /**
   * 提交状态变更校验逻辑
   * @param id
   * @param reqUser
   * @param submitStatus
   */
  async beforeUpdateSubmitStatus(
    id: string,
    reqUser: IReqUser,
    submitStatus: SubmitStatus
  ) {
    if (submitStatus === SubmitStatus.SUBMITTED) {
      // 提交状态校验
      await this.beforeSubmit(id, reqUser);
    } else {
      // 取消提交状态校验
      await this.beforeUnSubmit(id, reqUser);
    }
  }

  async beforeSubmit(id: string, reqUser: IReqUser) {
    const detailItem = await this.prisma.materialSettlementDetail.findFirst({
      where: {
        settlementId: id,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      }
    });
    if (!detailItem) {
      throw new BadRequestException('单据没有明细，不能提交');
    }
  }

  async beforeUnSubmit(id: string, reqUser: IReqUser) {
    const [billItem] = await this.getMaterialSettlementList(reqUser, { id });
    if (billItem.auditStatus === AuditStatus.APPROVED) {
      // 已审批不可取消提交
      throw new BadRequestException('已审批的单据，不能取消提交');
    }
  }

  /**
   * 修改审批状态
   * @param id
   * @param reqUser
   * @param data
   */
  async updateAuditStatus(
    id: string,
    reqUser: IReqUser,
    auditStatus: AuditStatus
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    // 审批状态变更校验逻辑
    await this.beforeUpdateAuditStatus(id, reqUser, auditStatus);
    await this.prisma.materialSettlement.update({
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      },
      data: {
        auditStatus,
        updateBy: userId
      }
    });
    return true;
  }

  async beforeUpdateAuditStatus(
    id: string,
    reqUser: IReqUser,
    auditStatus: AuditStatus
  ) {
    const [billItem] = await this.getMaterialSettlementList(reqUser, { id });
    if (
      auditStatus !== AuditStatus.APPROVED &&
      billItem.auditStatus === AuditStatus.APPROVED
    ) {
      const rets = await this.getMaterialSettlementList(reqUser, {
        supplierId: billItem.supplierId,
        contractId: billItem.contractId
      });

      if (rets[0].id !== id) {
        throw new BadRequestException('已有新的单据，历史单据不可操作');
      }

      const notApprovedBillItems = rets.filter(
        (item) => item.auditStatus !== AuditStatus.APPROVED && item.id !== id
      );
      if (notApprovedBillItems.length) {
        throw new BadRequestException('相同合同结算单存在未审批，不能撤销审批');
      }
    }
  }
}
