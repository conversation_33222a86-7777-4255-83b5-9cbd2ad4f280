import { Injectable } from '@nestjs/common';
import { isEmpty } from 'lodash';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import {
  AuditStatus,
  ContractTemplateClassifyType,
  MaterialType,
  Prisma,
  PurchaseType
} from '@/prisma/generated';

import {
  InspectionMaterialDetailListResponseDto,
  MaterialCategoryListResponseDto,
  QueryMaterialAllocationFromDetailListDto
} from '../material-incoming-inspection.dto';

@Injectable()
export class MaterialIncomingInspectionDetailRepository {
  constructor(private readonly prisma: PrismaService) {}

  // 获取甲方信息（“建设单位”）
  async selectPartAInfo(reqUser: IReqUser) {
    const partAInfo: Record<string, any>[] = await this.prisma.$queryRaw`
    select
      base_field.id
      ,base_ledger.value
    from basic_project_info_field_detail as base_field
    join basic_project_info_ledger as base_ledger
      on base_ledger.is_deleted = false
      and base_ledger.org_id = ${reqUser.orgId}
      and base_ledger.basic_project_info_field_detail_id = base_field.id
    where base_field.is_deleted = false
      and base_field.name = '建设单位'
  `;

    return partAInfo[0];
  }

  // 获取调入组织信息
  async selectMaterialAllocationOrgInfo(reqUser: IReqUser) {
    const result = await this.prisma.$queryRaw<any[]>`
      select distinct
        o.id
        ,o.seal_name as name
      from material_allocation_from maf
      join platform_meta.org o
        on o.tenant_id = maf.tenant_id
        and o.id = maf.org_id
      where maf.is_deleted = false
        and maf.tenant_id = ${reqUser.tenantId}
        and maf.transfer_in_project_id = ${reqUser.orgId}
      order by o.id
    `;

    return result;
  }

  // 获取最新的(合同/补充协议)
  async selectLastContractInfo(
    reqUser: IReqUser,
    contractId: string
  ): Promise<Record<string, string>> {
    const contractInfo: Record<string, string>[] = await this.prisma.$queryRaw`
      select
        coalesce((
          select max(child.id)
          from material_contract child
          where child.is_deleted = false
            and child.tenant_id = parent.tenant_id
            and child.org_id = parent.org_id
            and child.parent_id = parent.id
            and child.audit_status = ${AuditStatus.APPROVED}::"AuditStatus"
        ), parent.id) as id
      from material_contract parent
      where parent.is_deleted = false
        and parent.tenant_id = ${reqUser.tenantId}
        and parent.org_id = ${reqUser.orgId}
        and parent.id = ${contractId}
        and parent.audit_status = ${AuditStatus.APPROVED}::"AuditStatus"
    `;
    return contractInfo[0];
  }

  // 根据合同，获取合同下选择的材料类别信息（树级）
  async selectContractMaterialCategories(
    reqUser: IReqUser,
    contractId: string,
    keyword: string
  ): Promise<MaterialCategoryListResponseDto[]> {
    const result: MaterialCategoryListResponseDto[] = await this.prisma
      .$queryRaw`
      with temp_consumer_material as (
        select
          material_dictionary_category_id
        from contract_consume_material_details
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and org_id = ${reqUser.orgId}
          and material_contract_id = ${contractId}
      )
      , temp_contract_concrete_material as (
        select
          material_dictionary_category_id
        from contract_concrete_details
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and org_id = ${reqUser.orgId}
          and material_contract_id = ${contractId}
      )
      ,temp_turnover_material as (
        select
          material_dictionary_category_id
        from contract_turnover_material_details
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and org_id = ${reqUser.orgId}
          and material_contract_id = ${contractId}
      )
      , leaf_material_category as (
        select distinct
          material_dictionary_version_id
          ,full_id
        from material_dictionary_category
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and id in (
            select material_dictionary_category_id from(
              select material_dictionary_category_id from temp_consumer_material
              union all
              select material_dictionary_category_id from temp_contract_concrete_material
              union all
              select material_dictionary_category_id from temp_turnover_material 
            ) tt
          )
      )
      select distinct
        parent.id
        ,parent.parent_id
        ,parent.name
        ,parent.code
        ,parent.type
        ,parent.level
        ,parent.remark
        ,parent.level
        ,parent.sort
      from material_dictionary_category parent
      join leaf_material_category child
        on child.material_dictionary_version_id = parent.material_dictionary_version_id
        and POSITION(parent.id in child.full_id) > 0
      where parent.is_deleted = false
        and parent.tenant_id = ${reqUser.tenantId}
        ${keyword ? Prisma.sql`AND parent.name ILIKE ${`%${keyword}%`}` : Prisma.empty}
      order by parent.level, parent.sort
    `;

    return result;
  }

  // 查询项目成本核算设置的全部材料分类
  async selectAllMaterialCategories(
    reqUser: IReqUser,
    materialType: MaterialType | null,
    keyword: string
  ): Promise<MaterialCategoryListResponseDto[]> {
    const result: MaterialCategoryListResponseDto[] = await this.prisma
      .$queryRaw`
      with leaf_material_category as (
        select distinct mdc.full_id
        from material_dictionary_category mdc
        join material_dictionary_detail mdd
          on mdd.is_deleted = false
          and mdd.tenant_id = mdc.tenant_id
          and mdd.org_id = mdc.org_id
          and mdd.material_dictionary_category_id = mdc.id
          ${
            materialType
              ? Prisma.sql`and mdd.type = ${materialType}::"MaterialType"`
              : Prisma.sql`and mdd.type <> ${MaterialType.CONCRETE}::"MaterialType"`
          }
        where mdc.is_deleted = false
          and mdc.tenant_id = ${reqUser.tenantId}
          and mdc.material_dictionary_version_id in (
            select version_id from account_material_dictionary_version
            where is_deleted = false
              and tenant_id = ${reqUser.tenantId}
              and org_id = ${reqUser.orgId}
          )
      )
      select distinct
        mdc.id
        ,mdc.parent_id
        ,mdc.name
        ,mdc.code
        ,mdc.type
        ,mdc.level
        ,mdc.remark
        ,mdc.sort
      from material_dictionary_category mdc
      join leaf_material_category
        on position(mdc.full_id in leaf_material_category.full_id) > 0
      where mdc.is_deleted = false
        and mdc.is_active = true
        and mdc.tenant_id = ${reqUser.tenantId}
        ${keyword ? Prisma.sql`AND mdc.name ILIKE ${`%${keyword}%`}` : Prisma.empty}
      order by mdc.level, mdc.sort
    `;

    return result;
  }

  // 获取合同下指定材料分类的材料明细
  async selectContractMaterials(
    reqUser: IReqUser,
    materialCategoryId: string | null,
    contractId: string,
    keyword: string
  ): Promise<InspectionMaterialDetailListResponseDto[]> {
    const result: InspectionMaterialDetailListResponseDto[] = await this.prisma
      .$queryRaw`
      with temp_consumer_material as (
        select
          material_dictionary_detail_id
          ,unit
        from contract_consume_material_details
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and org_id = ${reqUser.orgId}
          and material_contract_id = ${contractId}
      )
      , temp_contract_concrete_material as (
        select
          material_dictionary_detail_id
          ,unit
        from contract_concrete_details
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and org_id = ${reqUser.orgId}
          and material_contract_id = ${contractId}
      )
      ,temp_turnover_material as (
        select
          material_dictionary_detail_id
          ,unit
        from contract_turnover_material_details
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and org_id = ${reqUser.orgId}
          and material_contract_id = ${contractId}
      )
      ,temp_contract_material as (
        select material_dictionary_detail_id, unit from temp_consumer_material
        union all
        select material_dictionary_detail_id, unit from temp_contract_concrete_material
        union all
        select material_dictionary_detail_id, unit from temp_turnover_material
      )
      ,temp_material_category as (
        select distinct id
        from material_dictionary_category
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and position(${materialCategoryId} in full_id) > 0
      )
      select distinct
        m.id
        ,m.name
        ,m.code
        ,m.specification_model as spec
        ,tcm.unit
        ,m.remark
        ,m.material_dictionary_category_id
        ,m.sort
        ,m.type
      from material_dictionary_detail m
      join temp_contract_material tcm
        on tcm.material_dictionary_detail_id = m.id
      where m.is_deleted = false
        and m.tenant_id = ${reqUser.tenantId}
        ${
          materialCategoryId
            ? Prisma.sql`and m.material_dictionary_category_id in (select id from temp_material_category)`
            : Prisma.empty
        }
        ${keyword ? Prisma.sql`AND m.name ILIKE ${`%${keyword}%`}` : Prisma.empty}
      order by m.material_dictionary_category_id, m.sort
    `;

    return result;
  }

  // 获取材料分类的所有材料明细
  async selectAllMaterials(
    reqUser: IReqUser,
    materialCategoryId: string | null,
    materialType: string | null,
    keyword: string
  ): Promise<InspectionMaterialDetailListResponseDto[]> {
    const result: InspectionMaterialDetailListResponseDto[] = await this.prisma
      .$queryRaw`
      with temp_material_category as (
        select distinct id
        from material_dictionary_category
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and position(${materialCategoryId} in full_id) > 0
      )
      select
        md.id
        ,md.code
        ,md.name
        ,md.specification_model as spec
        ,md.metering_unit as unit
        ,md.remark
        ,md.type
      from material_dictionary_detail md
      where md.is_deleted = false
        and md.tenant_id = ${reqUser.tenantId}
        and md.material_dictionary_version_id in (
          select version_id from account_material_dictionary_version
          where is_deleted = false
            and tenant_id = ${reqUser.tenantId}
            and org_id = ${reqUser.orgId}
        )
        ${
          materialType
            ? Prisma.sql`and md.type = ${materialType}::"MaterialType"`
            : Prisma.sql`and md.type <> ${MaterialType.CONCRETE}::"MaterialType"`
        }
        ${
          materialCategoryId
            ? Prisma.sql`and md.material_dictionary_category_id in (select id from temp_material_category)`
            : Prisma.empty
        }
        ${keyword ? Prisma.sql`AND md.name ILIKE ${`%${keyword}%`}` : Prisma.empty}
      order by md.material_dictionary_category_id, md.sort
    `;

    return result;
  }

  // 获取可选择的调拨单列表
  async selectMaterialAllocationFromList(
    tenantId: string,
    orgId: string,
    supplierId: string
  ) {
    const result = this.prisma.$queryRaw<any[]>`
      with temp_material_categories as (
        select
          mafd.material_allocation_from_id
          ,STRING_AGG(distinct
            case
              when position('|' in mdc.full_name) > 0
              then split_part(mdc.full_name, '|', 2)
              else split_part(mdc.full_name, '|', 1)
            end,
            ','
          ) filter (where mdc.full_name is not null) material_categories
        from material_allocation_from_detail mafd
        join material_dictionary_detail mdd
          on mdd.id = mafd.material_id
          and mdd.is_deleted = false
          and mdd.tenant_id = ${tenantId}
        join material_dictionary_category mdc
          on mdc.id = mdd.material_dictionary_category_id
          and mdc.is_deleted = false
          and mdc.tenant_id = ${tenantId}
          and mdc.org_id = mdd.org_id
        where mafd.is_deleted = false
          and mafd.tenant_id = ${tenantId}
          and mafd.org_id = ${supplierId}
          and COALESCE(mafd.parent_id,'') = ''
        group by mafd.material_allocation_from_id
      )
      select
        maf.id
        ,maf.code
        ,TO_CHAR(MAKE_DATE(year, month, day),'YYYY-MM-DD') as create_date
        ,u.nickname as creator
        ,temp_material_categories.material_categories
      from material_allocation_from as maf
      join platform_meta.user as u
        on u.id = maf.create_by
      left join temp_material_categories
        on temp_material_categories.material_allocation_from_id = maf.id
      where maf.is_deleted = false
        and maf.tenant_id = ${tenantId}
        and maf.org_id = ${supplierId}
        and maf.transfer_in_project_id = ${orgId}
        and not exists (
          select 1 from material_incoming_inspection_detail as miid
          where miid.is_deleted = false
            and miid.tenant_id = ${tenantId}
            and miid.org_id = ${orgId}
            and miid.material_allocation_from_id = maf.id
        )
    `;
    return result;
  }

  // 获取调拨材料明细
  async selectMaterialAllocationFromDetails(
    tenantId: string,
    body: QueryMaterialAllocationFromDetailListDto
  ) {
    const { materialAllocationFromIds, supplierId } = body;
    const result = await this.prisma.$queryRaw<any[]>`
      select
        maf.material_id
        ,maf.material_name
        ,maf.material_spec
        ,maf.unit
        ,maf.allocation_quantity
        ,maf.material_allocation_from_id
        ,mdd.code as material_code
      from material_allocation_from_detail maf
      join material_dictionary_detail mdd
        on mdd.is_deleted = false
        and mdd.tenant_id = maf.tenant_id
        and mdd.id = maf.material_id
      where maf.is_deleted = false
        and maf.tenant_id = ${tenantId}
        and maf.org_id = ${supplierId}
        ${isEmpty(materialAllocationFromIds) ? '' : `and maf.material_allocation_from_id in (${Prisma.join(materialAllocationFromIds)})`}
      order by maf.material_allocation_from_id, maf.order_no
    `;

    return result;
  }

  // 获取验收单明细可选择的单位下拉列表
  async selectInspectionDetailUnits(
    tenantId: string,
    orgId: string,
    contractId: string | null,
    materialIds: string[]
  ) {
    const result: any[] = await this.prisma.$queryRaw`
      with material_unit as (
        select
          id as material_id
          ,metering_unit as unit
        from material_dictionary_detail
        where is_deleted = false
          and id in (${Prisma.join(materialIds)})
      )
      , material_dictionary_unit as (
        select
          material_dictionary_detail_id as material_id
          ,unit
        from material_dictionary_unit_calculation
        where is_deleted = false
          and tenant_id = ${tenantId}
          and material_dictionary_detail_id in (${Prisma.join(materialIds)})
      )
      , material_contract_unit as (
        select
          material_detail_id as material_id
          ,unit
        from material_contract_unit_calculation
        where is_deleted = false
          and tenant_id = ${tenantId}
          and org_id = ${orgId}
          and material_contract_id = ${contractId || '-1'}
          and material_detail_id in (${Prisma.join(materialIds)})
      )
      select
        tt.material_id
        ,string_agg(distinct tt.unit, ',') optional_units
      from (
        select * from material_unit
        union all
        select * from material_dictionary_unit
        union all
        select * from material_contract_unit
      ) tt
      group by tt.material_id
    `;

    return result;
  }

  // 获取合同编制的合同列表(不含补充协议)
  async selectContracts(
    tenantId: string,
    orgId: string,
    contractTemplateClassifyType: ContractTemplateClassifyType,
    purchaseType: PurchaseType
  ) {
    const result: any[] = await this.prisma.$queryRaw`
      select
        mc.id
        ,mc.name
        ,mc.party_b
        ,mc.party_b_type
        ,mc.party_b_end_name
      from material_contract as mc
      join contract_template as ct
        on ct.is_deleted = false
        and ct.tenant_id = mc.tenant_id
        and ct.id = mc.contract_template_id
        and ct.classify = ${contractTemplateClassifyType}::"ContractTemplateClassifyType"
      where mc.is_deleted = false
        and mc.tenant_id = ${tenantId}
        and mc.org_id = ${orgId}
        and mc.parent_id is null
        and mc.audit_status = ${AuditStatus.APPROVED}::"AuditStatus"
        and not exists (
          select
          from material_incoming_inspection as mii
          where mii.is_deleted = false
            and mii.tenant_id = mc.tenant_id
            and mii.org_id = mc.org_id
            and mii.supplier_id = mc.party_b
            and mii.purchase_type != ${purchaseType}::"PurchaseType"
        )
    `;

    return result;
  }
}
