import { Injectable } from '@nestjs/common';

import { PrismaService } from '@/common/modules/prisma/prisma.service';

@Injectable()
export class MaterialIncomingInspectionImportRepository {
  constructor(private readonly prisma: PrismaService) {}

  // 根据年月分组，取进场验收单的最大的最后三位编码
  async selectInspectionCodeNo(tenantId: string, orgId: string) {
    const result: any[] = await this.prisma.$queryRaw`
      select
        year
        ,month
        ,max(right(code, 3)) as code_no
      from material_incoming_inspection
      where is_deleted = false
        and tenant_id = ${tenantId}
        and org_id = ${orgId}
      group by year, month
    `;

    return result;
  }

  async selectMaterialUnit(tenantId: string, orgId: string) {
    const result: any[] = await this.prisma.$queryRaw`
      with material_unit as (
        select
          id as material_id
          ,metering_unit as unit
        from material_dictionary_detail
        where is_deleted = false
          and material_dictionary_version_id in (
            select version_id from account_material_dictionary_version
            where tenant_id = ${tenantId}
              and org_id = ${orgId}
          )
      )
      , material_dictionary_unit as (
        select
          material_dictionary_detail_id as material_id
          ,unit
        from material_dictionary_unit_calculation
        where is_deleted = false
          and tenant_id = ${tenantId}
          and material_dictionary_detail_id in (select material_id from material_unit)
      )
      select
        tt.material_id
        ,string_agg(distinct tt.unit, ',') optional_units
      from (
        select * from material_unit
        union all
        select * from material_dictionary_unit
      ) tt
      group by tt.material_id
    `;

    return result;
  }

  async selectContractMaterialUnit(tenantId: string, orgId: string) {
    const result: any[] = await this.prisma.$queryRaw`
      select
        material_contract_id
        ,material_detail_id as material_id
        ,string_agg(distinct unit, ',') optional_units
      from material_contract_unit_calculation
      where is_deleted = false
        and tenant_id = ${tenantId}
        and org_id = ${orgId}
      group by material_contract_id, material_id
    `;

    return result;
  }
}
