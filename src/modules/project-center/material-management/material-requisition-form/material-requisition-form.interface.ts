import { InputType } from '@ewing/infra-cloud-sdk';

export interface SelectMaterialReceiveInfo {
  id: string;
  code: string;
  receivingDate: string;
  detailId: string;
  materialId: string;
  materialName: string;
  materialSpec: string;
  unit: string;
  price: string;
  requisitionQuantity: string;
  inventoryQuantity: string;
}

export interface SelectReturnInventoryInfo {
  id: string;
  code: string;
  returnInventoryDate: string;
  detailId: string;
  materialReceivingId: string;
  materialId: string;
  materialName: string;
  materialSpec: string;
  unit: string;
  price: string;
  inventoryQuantity: string;
}

export interface UpdateReceiveDetail {
  id: string;
  inventoryQuantity: InputType;
  requisitionQuantity: InputType;
}

export interface UpdateReversalDetail {
  id: string;
  reversalInventoryQuantity: InputType;
}
