import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';
import { PlatformModule } from '@/modules/platform/platform.module';

import { MaterialRequisitionFormController } from './material-requisition-form.controller';
import { RequisitionFormRepository } from './repositories/requisition-form.repository';
import { RequisitionFormDetailRepository } from './repositories/requisition-form-detail.repository';
import { MaterialRequisitionFormService } from './services/requisition-form.service';
import { MaterialRequisitionFormAttachmentService } from './services/requisition-form-attachment.service';
import { MaterialRequisitionFormDetailService } from './services/requisition-form-detail.service';

@Module({
  imports: [PrismaModule, PlatformModule],
  controllers: [MaterialRequisitionFormController],
  providers: [
    MaterialRequisitionFormService,
    MaterialRequisitionFormDetailService,
    MaterialRequisitionFormAttachmentService,
    RequisitionFormRepository,
    RequisitionFormDetailRepository
  ]
})
export class MaterialRequisitionFormModule {}
