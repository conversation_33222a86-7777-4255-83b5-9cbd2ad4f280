import {
  BadRequestException,
  Injectable,
  NotFoundException
} from '@nestjs/common';
import { Decimal } from '@prisma/client/runtime/library';
import * as dayjs from 'dayjs';
import * as uuid from 'uuid';

import { CommonRepositories } from '@/common/common-repositories';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { DetailType, MaterialAllocationFrom } from '@/prisma/generated';

import {
  MaterialAllocationFormChooseCategoryTreeResDto,
  MaterialAllocationFormChooseDetailsDto,
  MaterialAllocationFormChooseDetailsQueryDto,
  MaterialAllocationFormChooseDetailsResDto,
  MaterialAllocationFormDetailCreateDto,
  MaterialAllocationFormDetailCreateListDto,
  MaterialAllocationFormDetailUpdateDto
} from './material-allocation-from-detail.dto';
import { MaterialAllocationFormDetailRepository } from './material-allocation-from-detail.repositories';

interface Material {
  materialId: string;
  materialName: string;
  materialSpec: string;
  unit: string;
  price: number;
  inventoryQuantity: number;
}

interface MaterialRecord {
  inventoryId: string;
  formDate: string;
  unit: string;
  price: number;
  receivingPrice: number;
  quantity: number;
  code: string;
  detailId: string;
  materialId: string;
  materialName: string;
  materialSpec: string;
  createAt: string;
  detailType: string;
}

@Injectable()
export class MaterialAllocationFromDetailService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly repository: MaterialAllocationFormDetailRepository
  ) {}

  /**
   * 获取调拨单下所有明细（子级）
   * @param id
   * @param reqUser
   * @returns
   */
  async getList(id: string, reqUser: IReqUser) {
    return await this.prisma.materialAllocationFromDetail.findMany({
      where: {
        parentId: null,
        materialAllocationFromId: id,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      }
    });
  }

  // 查询调拨单下收料明细的最早时间
  async getEarliestTime(reqUser: IReqUser): Promise<string | null> {
    return await this.repository.getEarliestTime(reqUser);
  }

  /**
   * 查询可选的材料分类
   * @param materialAllocationFromId
   * @param reqUser
   */
  async getChooseMaterialCategory(
    reqUser: IReqUser,
    materialAllocationFromId: string
  ): Promise<MaterialAllocationFormChooseCategoryTreeResDto[]> {
    const allocationForm = await this.getOne(materialAllocationFromId, reqUser);
    const materialCategoryList: MaterialAllocationFormChooseCategoryTreeResDto[] =
      await this.repository.getChooseMaterialCategory(reqUser, allocationForm);
    return materialCategoryList;
  }

  /**
   * 查询可选的材料明细
   * @param materialAllocationFromId
   * @param reqUser
   */
  async getChooseMaterialDetails(
    query: MaterialAllocationFormChooseDetailsQueryDto,
    reqUser: IReqUser
  ): Promise<MaterialAllocationFormChooseDetailsResDto[]> {
    const allocationForm = await this.getOne(
      query.materialAllocationFromId,
      reqUser
    );
    const materialDetailsList: MaterialAllocationFormChooseDetailsResDto[] =
      await this.repository.getChooseMaterialDetails(
        allocationForm,
        query.materialAllocationFromId,
        reqUser,
        query.categoryId
      );
    return await this.mergeMaterials(materialDetailsList);
  }

  async add(
    body: MaterialAllocationFormDetailCreateDto,
    reqUser: IReqUser
  ): Promise<any> {
    const { materialAllocationFromId, list } = body;
    const { tenantId, orgId, id: userId } = reqUser;
    const addList: any[] = [];
    const updateList: any[] = [];
    const updateReturnList: any[] = [];
    const allocationForm = await this.getOne(materialAllocationFromId, reqUser);
    const allocationDate =
      allocationForm.year +
      '-' +
      allocationForm.month +
      '-' +
      allocationForm.day;
    // 查询所有涉及到的材料的收料和退库
    const receivingAndReturnList =
      await this.repository.getMaterialReceivingAndReturnList(
        allocationDate,
        reqUser,
        list
      );
    // 将数据进行map重组再排序
    const materialMap = await this.groupAndSortMaterials(
      receivingAndReturnList,
      list
    );
    // 判断父级是否存在
    for (const element of materialMap.keys()) {
      const obj = await this.notHasParent(
        reqUser,
        materialAllocationFromId,
        element,
        materialMap.get(element) || []
      );
      // 数据计算
      addList.push(...obj.addList);
      updateList.push(...obj.updateList);
      updateReturnList.push(...obj.updateReturnList);
    }
    await this.prisma.$transaction(async (tx) => {
      // 新增数据
      await tx.materialAllocationFromDetail.createMany({
        data: addList
      });
      // 修改库存
      for (const element of updateList) {
        await tx.materialReceivingInventory.update({
          where: {
            id: element.id,
            tenantId: reqUser.tenantId,
            orgId: reqUser.orgId
          },
          data: {
            inventoryQuantity: {
              decrement: element.quantity
            },
            createBy: userId,
            updateBy: userId
          }
        });
      }
      // 修改退库单库存
      for (const element of updateReturnList) {
        await tx.materialReturnInventoryFormDetail.update({
          where: {
            id: element.id,
            tenantId,
            orgId
          },
          data: {
            reversalInventoryQuantity: {
              decrement: element.quantity
            },
            createBy: userId,
            updateBy: userId
          }
        });
      }
    });
    // 计算并统计金额写入单据
    // 查询单据下所有的明细
    const receivingAmount =
      await this.prisma.materialAllocationFromDetail.aggregate({
        where: {
          materialAllocationFromId,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          isDeleted: false,
          parentId: null
        },
        _sum: {
          allocationAmount: true
        }
      });
    await this.prisma.materialAllocationFrom.update({
      where: {
        id: materialAllocationFromId,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      },
      data: {
        amount: receivingAmount._sum.allocationAmount,
        updateBy: reqUser.id
      }
    });
    return true;
  }

  async update(
    id: string,
    body: MaterialAllocationFormDetailUpdateDto,
    reqUser: IReqUser
  ) {
    // 校验明细
    const detail = await this.prisma.materialAllocationFromDetail.findUnique({
      where: {
        id,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      }
    });
    if (!detail) {
      throw new NotFoundException('明细不存在');
    }
    if (
      Decimal(body.allocationQuantity).equals(detail.allocationQuantity || 0)
    ) {
      // 如果数据一致，则不处理，只更新备注
      await this.prisma.materialAllocationFromDetail.update({
        where: {
          id: id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        },
        data: {
          remark: body.remark
        }
      });
      return true;
    }
    const param: any = {
      materialName: detail.materialName,
      materialSpec: detail.materialSpec,
      unit: detail.unit,
      price: detail.inStockPrice
    };
    // 校验调拨数量
    await this.checkReturnQuantity(
      param,
      Decimal(body.allocationQuantity),
      detail.allocationQuantity || null,
      reqUser
    );
    // 删除现有的明细
    await this.delete(id, reqUser);
    // 查询现有库存数量
    const inStockQuantity = await this.repository.getInventory(param, reqUser);
    const inStockPrice = detail.inStockPrice
      ? Decimal(detail.inStockPrice).toNumber()
      : 0;
    // 重新创建
    const materialAllocationFormDetailCreateDto: MaterialAllocationFormDetailCreateDto =
      {
        list: [
          {
            materialId: detail.materialId,
            materialName: detail.materialName,
            materialSpec: detail.materialSpec,
            unit: detail.unit || '',
            inStockQuantity,
            inStockPrice,
            allocationQuantity: body.allocationQuantity,
            remark: body.remark,
            orderNo: detail.orderNo
          }
        ],
        materialAllocationFromId: detail.materialAllocationFromId
      };
    await this.add(materialAllocationFormDetailCreateDto, reqUser);
    return true;
  }

  async delete(id: string, reqUser: IReqUser) {
    // 校验父级
    const parentDetail = await this.getOneDetail(id, reqUser);
    // 查询改明细下的所有子级
    const children = await this.getDetailChildren(id, reqUser);
    // 数据调整
    const changeChildren = await this.getDetailChildrenList(children);
    const ids: string[] = [];
    children.forEach((item) => {
      ids.push(item.id);
    });
    ids.push(id);
    // 然后进行库存返回
    await this.prisma.$transaction(async (tx) => {
      for (const element of changeChildren) {
        if (element.detailType === DetailType.RECEIVING) {
          await tx.materialReceivingInventory.update({
            where: {
              id: element.detailId || ''
            },
            data: {
              inventoryQuantity: {
                increment: Decimal(element.allocationQuantity || 0)
              },
              updateBy: reqUser.id
            }
          });
        } else {
          await tx.materialReturnInventoryFormDetail.update({
            where: {
              id: element.detailId || ''
            },
            data: {
              reversalInventoryQuantity: {
                increment: Decimal(element.allocationQuantity || 0)
              },
              updateBy: reqUser.id
            }
          });
        }
      }
      await tx.materialAllocationFrom.update({
        where: {
          id: parentDetail.materialAllocationFromId,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          isDeleted: false
        },
        data: {
          amount: {
            decrement: Decimal(parentDetail.allocationAmount || 0)
          },
          updateBy: reqUser.id
        }
      });
      await tx.materialAllocationFromDetail.updateMany({
        where: {
          id: {
            in: ids
          }
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
    });
    return true;
  }

  async getDetailList(id: string, reqUser: IReqUser) {
    const res = await this.prisma.materialAllocationFromDetail.findMany({
      select: {
        id: true,
        materialAllocationFromId: true,
        parentId: true,
        detailId: true,
        detailType: true,
        detailCode: true,
        detailDate: true,
        materialId: true,
        materialName: true,
        materialSpec: true,
        unit: true,
        orderNo: true,
        inStockQuantity: true,
        inStockPrice: true,
        allocationAmount: true,
        allocationPrice: true,
        allocationQuantity: true,
        remark: true
      },
      where: {
        materialAllocationFromId: id,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      },
      orderBy: {
        orderNo: 'asc'
      }
    });
    return res.map((item) => {
      if (item.parentId) {
        item.materialName = item.detailCode || '';
        item.materialSpec = item.detailDate
          ? dayjs(item.detailDate).format('YYYY-MM-DD')
          : '';
      }
      return {
        ...item
      };
    });
  }

  async move(reqUser: IReqUser, fromId: string, toId: string) {
    await CommonRepositories.changeDataOrderNo(this.prisma, {
      tenantId: reqUser.tenantId,
      orgId: reqUser.orgId,
      fromId,
      toId,
      tableName: 'material_allocation_from_detail'
    });
    return true;
  }

  async getDetailChildrenList(children: any[]) {
    return children.map((item) => {
      if (item.detailType === DetailType.RECEIVING) {
        // 查看是否存在相应退库数量
        const target = children.find(
          (element) =>
            element.detailType === DetailType.RETURN_INVENTORY &&
            element.materialReceivingInventoryId ===
              item.materialReceivingInventoryId
        );
        if (target) {
          item.allocationQuantity += target?.allocationQuantity || 0;
        }
      }
      return {
        ...item
      };
    });
  }

  /**
   * 校验调拨数量
   */
  async checkReturnQuantity(
    data: any,
    allocationQuantity: Decimal,
    parentAllocationQuantity: Decimal | null,
    reqUser: IReqUser
  ) {
    // 查询现有库存数量
    const inventoryQuantity = Decimal(
      await this.repository.getInventory(data, reqUser)
    );
    // 该调拨单明细现存调拨数量
    const existingAllocationQuantity = Decimal(parentAllocationQuantity || 0);
    // 要变更的库存
    const updateAllocationQuantity = Decimal(allocationQuantity);
    if (
      existingAllocationQuantity.toNumber() <
      updateAllocationQuantity.toNumber()
    ) {
      // 要补的调拨数量
      const addAllocationQuantity = updateAllocationQuantity.minus(
        existingAllocationQuantity
      );
      if (inventoryQuantity < addAllocationQuantity) {
        // 现有库存不足，无法退
        throw new BadRequestException('当前库存数量不足，请重新输入调拨数量');
      }
    }
  }

  /**
   * 查询单个调拨单详情
   * @param id
   * @param reqUser
   * @returns
   */
  async getOneDetail(id: string, reqUser: IReqUser) {
    const allocationFormDetail =
      await this.prisma.materialAllocationFromDetail.findUnique({
        where: {
          id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        }
      });
    if (!allocationFormDetail) {
      throw new NotFoundException('未找到该调拨单明细');
    }
    if (allocationFormDetail.parentId) {
      throw new BadRequestException(`只有父级可以操作`);
    }
    return allocationFormDetail;
  }

  // 查询所有的父级明细
  async getParentList(materialAllocationFromId: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    return await this.prisma.materialAllocationFromDetail.findMany({
      where: {
        materialAllocationFromId: materialAllocationFromId,
        parentId: null,
        tenantId,
        orgId,
        isDeleted: false
      }
    });
  }

  /**
   * 父级不存在
   * @param data 数据
   * @param reqUser
   * @param materialAllocationFromId 调拨单id
   */
  async notHasParent(
    reqUser: IReqUser,
    materialAllocationFromId: string,
    materialData: MaterialAllocationFormDetailCreateListDto,
    data: MaterialRecord[]
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    const parentId = uuid.v7();
    // 新增的调拨数组
    const addList = [];
    // 编辑库存的数组
    const updateList = [];
    // 更新退库的数组
    const updateReturnList = [];
    // 创建父级
    const parent = {
      materialAllocationFromId,
      id: parentId,
      orgId,
      tenantId,
      materialId: materialData.materialId,
      materialName: materialData.materialName,
      materialSpec: materialData.materialSpec,
      unit: materialData.unit,
      inStockQuantity: materialData.inStockQuantity,
      inStockPrice: materialData.inStockPrice,
      allocationQuantity: materialData.allocationQuantity,
      allocationAmount: Decimal(0),
      allocationPrice: Decimal(0),
      createBy: userId,
      updateBy: userId,
      remark: materialData.remark,
      orderNo: materialData.orderNo
    };
    // 要调的数量
    let quantity = materialData.allocationQuantity || 0;
    // 判断子级
    // 将退库的数量在收料里面减去
    data = await this.processInventoryQuantities(data);
    for (const element of data) {
      // 要退的数量必须要大于0，小于0后停止
      if (quantity > 0) {
        // 先判断应该取退库单数据还是收料单数据
        if (element.detailType === DetailType.RECEIVING) {
          // 在库库存大于退库单库存，取退库单数据
          addList.push({
            orgId,
            tenantId,
            createBy: userId,
            updateBy: userId,
            parentId,
            materialAllocationFromId,
            detailId: element.detailId,
            detailType: DetailType.RECEIVING,
            detailCode: element.code,
            detailDate: new Date(element.createAt),
            materialId: element.materialId,
            materialName: element.materialName,
            materialReceivingInventoryId: element.inventoryId,
            materialSpec: element.materialSpec,
            unit: element.unit,
            inStockQuantity: element.quantity,
            inStockPrice: element.price,
            allocationQuantity:
              quantity > element.quantity ? element.quantity : quantity,
            allocationPrice: element.price,
            allocationAmount: Decimal(element.price).mul(
              quantity > element.quantity ? element.quantity : quantity
            )
          });
          updateList.push({
            id: element.inventoryId,
            quantity: quantity > element.quantity ? element.quantity : quantity
          });
          quantity -= element.quantity;
        } else {
          // 在库库存小于等于退库单库存
          addList.push({
            orgId,
            tenantId,
            createBy: userId,
            updateBy: userId,
            parentId,
            materialAllocationFromId,
            detailId: element.detailId,
            detailType: DetailType.RETURN_INVENTORY,
            detailCode: element.code,
            detailDate: new Date(element.createAt),
            materialId: element.materialId,
            materialName: element.materialName,
            materialReceivingInventoryId: element.inventoryId,
            materialSpec: element.materialSpec,
            unit: element.unit,
            inStockQuantity: element.quantity,
            inStockPrice: element.price,
            allocationQuantity:
              quantity > element.quantity ? element.quantity : quantity,
            allocationPrice: element.price,
            allocationAmount: Decimal(element.price).mul(
              quantity > element.quantity ? element.quantity : quantity
            )
          });
          updateList.push({
            id: element.inventoryId,
            quantity: quantity > element.quantity ? element.quantity : quantity
          });
          updateReturnList.push({
            id: element.detailId,
            quantity: quantity > element.quantity ? element.quantity : quantity
          });
          quantity -= element.quantity;
        }
      }
    }
    // 合计父级的金额
    parent.allocationAmount = addList.reduce(
      (pre, cur) => pre.add(cur.allocationAmount),
      new Decimal(0)
    );
    // 计算父级的单价
    parent.allocationPrice = parent.allocationAmount.div(
      parent.allocationQuantity
    );
    // 将父级放在首位
    addList.unshift(parent);
    return {
      addList,
      updateList,
      updateReturnList
    };
  }

  // 数据转换，将存在退库单的收料库存减去退库的库存
  async processInventoryQuantities(records: MaterialRecord[]) {
    // 按 inventoryId 分组
    const inventoryGroups = new Map<string, MaterialRecord[]>();

    records.forEach((record) => {
      if (!inventoryGroups.has(record.inventoryId)) {
        inventoryGroups.set(record.inventoryId, []);
      }
      inventoryGroups.get(record.inventoryId)!.push(record);
    });

    // 处理每个分组
    inventoryGroups.forEach((group) => {
      // 分别计算 RECEIVING 和 INVENTORY 的总 quantity
      const receivingRecords = group.filter(
        (r) => r.detailType === 'RECEIVING'
      );
      const inventoryRecords = group.filter(
        (r) => r.detailType === 'INVENTORY'
      );

      if (receivingRecords.length > 0 && inventoryRecords.length > 0) {
        // 计算总差值
        const totalReceiving = receivingRecords.reduce(
          (sum, r) => sum + r.quantity,
          0
        );
        const totalInventory = inventoryRecords.reduce(
          (sum, r) => sum + r.quantity,
          0
        );
        const difference = totalReceiving - totalInventory;

        // 更新最后一条 RECEIVING 记录的 quantity
        const lastReceivingRecord =
          receivingRecords[receivingRecords.length - 1];
        lastReceivingRecord.quantity = difference;
      }
    });

    return records;
  }

  // 将数据按照材料名称+材料规格+材料单价进行map重组再排序
  async groupAndSortMaterials(
    records: MaterialRecord[],
    parent: MaterialAllocationFormDetailCreateListDto[]
  ): Promise<Map<MaterialAllocationFormDetailCreateListDto, MaterialRecord[]>> {
    // 创建一个映射表，键为 materialName、materialSpec 和 price 的组合
    const materialMap = new Map<
      MaterialAllocationFormDetailCreateListDto,
      MaterialRecord[]
    >();

    parent.forEach((item) => {
      materialMap.set(item, []);
    });

    // 按 materialName、materialSpec 和 price 分组
    for (const element of materialMap.keys()) {
      records.forEach((record) => {
        if (
          element.materialId === record.materialId &&
          element.materialName === record.materialName &&
          element.materialSpec === record.materialSpec &&
          element.unit === record.unit
        ) {
          materialMap.get(element)!.push(record);
        }
      });
    }

    // 对每个分组内的记录进行排序
    materialMap.forEach((group) => {
      group.sort((a, b) => {
        // 先比较 createAt（降序）
        const dateA = new Date(a.createAt);
        const dateB = new Date(b.createAt);
        const dateDiff = dateB.getTime() - dateA.getTime();

        if (dateDiff !== 0) {
          return dateDiff; // 日期不同时，按日期降序
        }

        // 日期相同时，再比较 detailType（降序）
        return b.detailType.localeCompare(a.detailType);
      });
    });

    return materialMap;
  }

  // 数据合并
  private async mergeMaterials(
    materials: MaterialAllocationFormChooseDetailsDto[]
  ): Promise<Material[]> {
    // 创建一个映射表，键为组合的唯一标识，值为合并后的材料对象
    const mergedMap = new Map<string, Material>();

    materials.forEach((material) => {
      // 创建基于 material_id、material_name、material_spec、unit 和 price 的唯一键
      const key = `${material.materialId}-${material.materialName}-${material.materialSpec}-${material.unit}`;

      if (mergedMap.has(key)) {
        // 如果已存在相同组合的材料，则累加 inventory_quantity
        const existingMaterial = mergedMap.get(key)!;
        const total = new Decimal(existingMaterial.inventoryQuantity).plus(
          material.inventoryQuantity
        ); // 四舍五入保留8位
        existingMaterial.inventoryQuantity = Number(total);
      } else {
        // 如果不存在，则添加新的材料记录
        mergedMap.set(key, { ...material });
      }
    });

    // 将映射表中的值转换为数组返回
    return Array.from(mergedMap.values());
  }

  /**
   * 查询单个调拨单
   * @param id
   * @param reqUser
   * @returns
   */
  async getOne(id: string, reqUser: IReqUser): Promise<MaterialAllocationFrom> {
    const materialAllocationFrom =
      await this.prisma.materialAllocationFrom.findUnique({
        where: {
          id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        }
      });
    if (!materialAllocationFrom) {
      throw new NotFoundException('未找到该调拨单');
    }
    return materialAllocationFrom;
  }

  /**
   * 查询父级明细下的所有子级
   * @param id
   * @param reqUser
   * @returns
   */
  async getDetailChildren(id: string, reqUser: IReqUser) {
    return await this.prisma.materialAllocationFromDetail.findMany({
      where: {
        parentId: id,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      }
    });
  }
}
