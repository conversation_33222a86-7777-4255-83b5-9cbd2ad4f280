import { ApiProperty, PickType } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsBoolean,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString
} from 'class-validator';

import {
  AuditStatus,
  MaterialSettlementStatus,
  SubmitStatus
} from '@/prisma/generated';

export class BaseMaterialAllocationFormDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '单据编码' })
  @IsNotEmpty({ message: '单据编码不能为空' })
  @IsString({ message: '单据编码必须是字符串' })
  code: string;

  @ApiProperty({ description: '结算状态' })
  @IsNotEmpty({ message: '结算状态不能为空' })
  @IsIn(Object.values(MaterialSettlementStatus), {
    message: '结算状态必须是有效枚举值'
  })
  @IsString({ message: '采购结算状态必须是字符串' })
  materialSettlementStatus: MaterialSettlementStatus;

  @ApiProperty({ description: '调入项目名称' })
  transferInProjectName?: string;

  @ApiProperty({ description: '调入项目Id' })
  @IsOptional({ message: '调入项目Id可以为空' })
  @IsString({ message: '调入项目Id必须是字符串' })
  transferInProjectId?: string;

  @ApiProperty({ description: '调出项目名称' })
  transferOutProjectName?: string;

  @ApiProperty({ description: '材料类别' })
  materialCategories: string;

  @ApiProperty({ description: '调拨金额' })
  @IsOptional({ message: '调拨金额可以为空' })
  @IsNumber({}, { message: '调拨金额必须是数字' })
  amount?: number;

  @ApiProperty({ description: '提交状态' })
  @IsNotEmpty({ message: '提交状态不能为空' })
  @IsIn(Object.values(SubmitStatus), {
    message: '提交状态必须是有效枚举值'
  })
  @IsString({ message: '提交状态必须是字符串' })
  submitStatus: SubmitStatus;

  @ApiProperty({ description: '审批状态' })
  @IsNotEmpty({ message: '审批状态不能为空' })
  @IsIn(Object.values(AuditStatus), {
    message: '审批状态必须是有效枚举值'
  })
  @IsString({ message: '审批状态必须是字符串' })
  auditStatus: AuditStatus;

  @ApiProperty({ description: '年' })
  @IsOptional({ message: '年可以为空' })
  @Type(() => Number)
  @IsNumber({}, { message: '年必须是数字' })
  year?: number;

  @ApiProperty({ description: '月' })
  @IsOptional({ message: '月可以为空' })
  @Type(() => Number)
  @IsNumber({}, { message: '月必须是数字' })
  month?: number;

  @ApiProperty({ description: '日' })
  @IsOptional({ message: '日可以为空' })
  @Type(() => Number)
  @IsNumber({}, { message: '日必须是数字' })
  day?: number;

  @ApiProperty({ description: '创建人名称' })
  creator: string;

  @ApiProperty({ description: '编制时间' })
  createAt: Date;
}

export class QueryMaterialAllocationFormDto extends PickType(
  BaseMaterialAllocationFormDto,
  ['year', 'month', 'day']
) {
  @ApiProperty({ description: '是否仅查看自己的数据,默认为false' })
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  onlyViewSelf: boolean;
}

export class MaterialAllocationFormResDto extends BaseMaterialAllocationFormDto {}

export class MaterialAllocationFormUpdateDto extends PickType(
  BaseMaterialAllocationFormDto,
  ['transferInProjectId', 'amount', 'year', 'month', 'day']
) {
  @ApiProperty({
    description: '调拨单编码'
  })
  code: string;
}

export class MaterialAllocationFormUpdateSubmitStatusDto extends PickType(
  BaseMaterialAllocationFormDto,
  ['submitStatus']
) {}

export class MaterialAllocationFormUpdateAuditStatusDto extends PickType(
  BaseMaterialAllocationFormDto,
  ['auditStatus']
) {}
