{"name": "ecost-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "prisma generate && nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "prisma generate && nest start --watch", "start:debug": "prisma generate && nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prepare": "husky"}, "lint-staged": {"{src,test}/**/*.ts": ["prettier --write", "eslint --fix"]}, "dependencies": {"@ewing/infra-cloud-sdk": "^0.0.20", "@fastify/multipart": "^9.0.3", "@fastify/request-context": "^6.1.0", "@fastify/static": "^8.1.1", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.1", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-fastify": "^11.0.1", "@nestjs/swagger": "^11.0.6", "@prisma/client": "6.9.0", "@types/multer": "^1.4.12", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dayjs": "^1.11.13", "esdk-obs-nodejs": "^3.24.3", "exceljs": "^4.4.0", "fastify": "^5.2.1", "joi": "^17.13.3", "lodash": "^4.17.21", "lodash.isempty": "^4.4.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pattern-matching": "^0.1.6", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "eslint-plugin-simple-import-sort": "^12.1.1", "globals": "^15.14.0", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^16.0.0", "prettier": "^3.4.2", "prisma": "6.9.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "prisma": {"schema": "./prisma"}, "packageManager": "pnpm@10.11.0"}